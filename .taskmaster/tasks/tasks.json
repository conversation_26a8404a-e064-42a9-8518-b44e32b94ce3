{"master": {"tasks": [{"id": 30, "title": "Create Subscription Mapping Service", "description": "Develop a service that maps between legacy PaymentPlanId enum and new Subscription UUIDs to maintain compatibility between systems.", "details": "Implement a SubscriptionMappingService class that provides bidirectional mapping between PaymentPlanId enum values and Subscription UUIDs. The service should include methods like `getSubscriptionIdFromPlanId(planId: PaymentPlanId): UUID` and `getPlanIdFromSubscriptionId(subscriptionId: UUID): PaymentPlanId`. Store mappings in a database table for persistence. Use a singleton pattern to ensure consistent access across the application.", "testStrategy": "Create unit tests for all mapping functions with test cases covering all possible PaymentPlanId values. Implement integration tests that verify database persistence of mappings. Test edge cases like non-existent mappings and error handling.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 31, "title": "Initialize Subscription Plan Mappings in Database", "description": "Create and populate the database with initial mappings between legacy payment plans and new subscription records.", "status": "in-progress", "dependencies": [30], "priority": "high", "details": "Update the subscription seed script to ensure it properly creates subscription plans with names that match the PaymentPlanId enum values. This will allow the existing SubscriptionMappingService to create mappings by matching subscription names. Steps include: 1) Review the PaymentPlanId enum values and ensure corresponding subscription plans are created with matching names. 2) Verify the subscription seed script creates all necessary subscription records. 3) Run the database migration and seeding to populate the subscription data. 4) Ensure the SubscriptionMappingService can correctly map between legacy payment plans and new subscription records.", "testStrategy": "Verify all subscription plans are correctly created with names matching PaymentPlanId enum values. Test that the SubscriptionMappingService correctly maps between legacy payment plans and new subscription records. Create a validation script that checks for mapping completeness by ensuring every PaymentPlanId enum value has a corresponding subscription record with a matching name. Test both forward and reverse mapping functionality.", "subtasks": [{"id": 1, "title": "Review PaymentPlanId enum values and corresponding subscription names", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}, {"id": 2, "title": "Update subscription seed script to ensure correct name matching", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}, {"id": 3, "title": "Run database migration and seeding to verify subscription creation", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}, {"id": 4, "title": "Test SubscriptionMappingService with created subscription records", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}, {"id": 5, "title": "Create validation script to verify mapping completeness", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}]}, {"id": 32, "title": "Implement Subscription Synchronization Service", "description": "Create a service to maintain data consistency between legacy User model subscription fields and new UserSubscription records.", "details": "Develop a SubscriptionSyncService with methods to: 1) syncUserSubscriptionToLegacy(userId: UUID): void - Updates User model fields based on UserSubscription data. 2) syncLegacyToUserSubscription(userId: UUID): void - Creates/updates UserSubscription based on User model fields. 3) validateSubscriptionConsistency(userId: UUID): boolean - Checks if both systems have consistent data. Implement with transaction support to prevent partial updates.", "testStrategy": "Create unit tests for each synchronization method. Test scenarios including new subscriptions, upgrades, downgrades, and cancellations. Implement integration tests that verify database consistency after sync operations. Test error handling and transaction rollback.", "priority": "high", "dependencies": [30, 31], "status": "pending", "subtasks": []}, {"id": 33, "title": "Create Utility Functions for Status Mapping and Data Conversion", "description": "Develop utility functions to map subscription statuses and convert data formats between legacy and new subscription systems.", "details": "Implement a SubscriptionUtilities module with functions for: 1) mapLegacyStatusToNew(legacyStatus: string): SubscriptionStatus - Converts legacy status strings to new enum values. 2) mapNewStatusToLegacy(newStatus: SubscriptionStatus): string - Converts new status enum to legacy format. 3) convertLegacySubscriptionData(userData: User): UserSubscriptionData - Extracts subscription data from User model. 4) formatSubscriptionMetadata(subscription: Subscription): object - Creates metadata for payment processor.", "testStrategy": "Create comprehensive unit tests for all utility functions with test cases covering all possible status values and data formats. Test edge cases and error handling. Verify bidirectional conversion maintains data integrity.", "priority": "medium", "dependencies": [30], "status": "pending", "subtasks": []}, {"id": 34, "title": "Enhance CreateCheckoutSessionArgs Interface", "description": "Update the CreateCheckoutSessionArgs interface to include subscription metadata needed for the new subscription system.", "details": "Modify the CreateCheckoutSessionArgs interface to include optional subscription-related fields: 1) subscriptionId?: UUID - The ID of the subscription being purchased. 2) subscriptionMetadata?: object - Additional metadata for the subscription. 3) isLegacyMode?: boolean - Flag to indicate if using legacy or new subscription flow. Update all implementations and usages of this interface throughout the codebase. Ensure backward compatibility by making new fields optional.", "testStrategy": "Update existing unit tests for checkout session creation to include the new fields. Create new test cases specifically for subscription-related checkout flows. Verify backward compatibility with existing code that uses the interface.", "priority": "high", "dependencies": [30, 33], "status": "pending", "subtasks": []}, {"id": 35, "title": "Update LemonSqueezy Webhook Handler for UserSubscription Creation", "description": "Enhance the LemonSqueezy webhook handler to create and update UserSubscription records in addition to updating legacy User fields.", "details": "Modify the handleLemonSqueezyWebhook function to: 1) Continue updating User model fields for backward compatibility. 2) Create or update UserSubscription records based on webhook events. 3) Use the SubscriptionMappingService to determine the correct Subscription ID. 4) Implement transaction-based updates to ensure consistency between User and UserSubscription tables. 5) Handle all relevant event types: subscription_created, subscription_updated, subscription_cancelled, etc.", "testStrategy": "Create integration tests with mock LemonSqueezy webhook payloads for each event type. Verify both User model and UserSubscription records are correctly updated. Test error scenarios and transaction rollback. Validate data consistency between systems after webhook processing.", "priority": "high", "dependencies": [30, 31, 32, 33], "status": "pending", "subtasks": []}, {"id": 36, "title": "Update Stripe Webhook Handler for UserSubscription Creation", "description": "Enhance the Stripe webhook handler to create and update UserSubscription records in addition to updating legacy User fields.", "details": "Modify the handleStripeWebhook function to: 1) Continue updating User model fields for backward compatibility. 2) Create or update UserSubscription records based on webhook events. 3) Use the SubscriptionMappingService to determine the correct Subscription ID. 4) Implement transaction-based updates to ensure consistency between User and UserSubscription tables. 5) Handle all relevant event types: customer.subscription.created, customer.subscription.updated, customer.subscription.deleted, etc.", "testStrategy": "Create integration tests with mock Stripe webhook payloads for each event type. Verify both User model and UserSubscription records are correctly updated. Test error scenarios and transaction rollback. Validate data consistency between systems after webhook processing.", "priority": "high", "dependencies": [30, 31, 32, 33], "status": "pending", "subtasks": []}, {"id": 37, "title": "Modify generateCheckoutSession Operation", "description": "Update the generateCheckoutSession operation to support both legacy and new subscription flows while maintaining backward compatibility.", "details": "Enhance the generateCheckoutSession function to: 1) Accept the enhanced CreateCheckoutSessionArgs with subscription metadata. 2) Determine whether to use legacy or new subscription flow based on args or feature flags. 3) For new flow, include subscription metadata in the checkout session. 4) For legacy flow, maintain existing behavior. 5) Return consistent response format regardless of flow used. 6) Add proper error handling for subscription-specific issues.", "testStrategy": "Create unit tests for both legacy and new subscription flows. Test with various subscription types and payment processors. Verify checkout session contains correct metadata. Test error handling and edge cases. Perform end-to-end tests with actual payment processor APIs in test mode.", "priority": "high", "dependencies": [34, 35, 36], "status": "pending", "subtasks": []}, {"id": 38, "title": "Implement Transaction-based Updates for Data Consistency", "description": "Create a transaction management system to ensure data consistency when updating both legacy and new subscription systems.", "details": "Implement a TransactionManager class that: 1) Provides methods for executing operations within a database transaction. 2) Handles rollback on failure. 3) Logs transaction details for debugging. 4) Supports nested transactions if needed. Modify subscription-related operations to use transactions when updating multiple tables. Implement specific transaction handlers for webhook processing, subscription creation, and status changes.", "testStrategy": "Create unit tests for transaction management functions. Test successful transactions and rollbacks. Implement integration tests that simulate failures at different points in the transaction to verify proper rollback. Verify data consistency after failed transactions.", "priority": "high", "dependencies": [32, 35, 36], "status": "pending", "subtasks": []}, {"id": 39, "title": "Enhance Subscription API Endpoints", "description": "Update subscription API endpoints to integrate with payment processor checkout flow and support both legacy and new subscription systems.", "details": "Modify existing subscription API endpoints to: 1) Support operations on both User model and UserSubscription records. 2) Add new endpoints for UserSubscription-specific operations. 3) Implement feature flag checks to determine which system to use. 4) Ensure consistent response formats regardless of underlying system. 5) Add proper error handling and validation. Key endpoints to update: getSubscriptions, getUserSubscription, updateSubscription, cancelSubscription.", "testStrategy": "Create API tests for each endpoint covering both legacy and new subscription flows. Test with various subscription types and states. Verify correct handling of feature flags. Test error scenarios and edge cases. Implement integration tests that verify database state after API operations.", "priority": "medium", "dependencies": [32, 37, 38], "status": "pending", "subtasks": []}, {"id": 40, "title": "Modify handleSubscribeToSubscription Function", "description": "Update the handleSubscribeToSubscription function to redirect to payment processor when needed and support both subscription systems.", "details": "Enhance handleSubscribeToSubscription to: 1) Check if payment is required based on subscription type. 2) For paid subscriptions, generate checkout session using the updated generateCheckoutSession. 3) For free subscriptions, create UserSubscription record directly. 4) Update legacy User fields for backward compatibility. 5) Handle upgrades, downgrades, and reactivations appropriately. 6) Implement proper error handling and validation.", "testStrategy": "Create unit tests for different subscription scenarios (new, upgrade, downgrade, free, paid). Test integration with payment processors using test mode. Verify correct redirection for paid subscriptions. Test error handling and edge cases. Validate data consistency between systems after subscription changes.", "priority": "high", "dependencies": [37, 39], "status": "pending", "subtasks": []}, {"id": 41, "title": "Create Direct Subscription Creation for Admin/Special Cases", "description": "Implement functionality for direct creation of UserSubscription records for administrative purposes or special cases without payment processor involvement.", "details": "Create an AdminSubscriptionService with methods to: 1) createUserSubscription(userId: UUID, subscriptionId: UUID, options: object): UserSubscription - Creates subscription without payment. 2) updateUserSubscriptionStatus(userSubscriptionId: UUID, status: SubscriptionStatus): void - Directly updates status. 3) extendUserSubscription(userSubscriptionId: UUID, durationDays: number): void - Extends subscription period. Implement proper authorization checks and audit logging for all admin operations.", "testStrategy": "Create unit tests for all admin subscription functions. Test authorization checks and permissions. Implement integration tests that verify database state after admin operations. Test synchronization with legacy User model fields. Verify audit logging functionality.", "priority": "medium", "dependencies": [32, 38], "status": "pending", "subtasks": []}, {"id": 42, "title": "Implement Subscription Status Synchronization", "description": "Create a system to ensure subscription status is synchronized between legacy and new subscription systems.", "details": "Implement a SubscriptionStatusSyncJob that: 1) Runs periodically (e.g., hourly) to check for inconsistencies. 2) Identifies users with mismatched subscription status between systems. 3) Applies corrections using the SubscriptionSyncService. 4) Logs discrepancies for review. 5) Sends alerts for critical inconsistencies. Also implement event-based synchronization that triggers on status changes in either system.", "testStrategy": "Create unit tests for the synchronization job logic. Test with various discrepancy scenarios. Implement integration tests that verify correction of inconsistencies. Test performance with large datasets. Verify logging and alerting functionality.", "priority": "medium", "dependencies": [32, 38], "status": "pending", "subtasks": []}, {"id": 43, "title": "Create Migration Scripts for Existing User Subscription Data", "description": "Develop scripts to migrate existing user subscription data from the legacy User model to new UserSubscription records.", "details": "Create a migration system with: 1) Analysis script to identify users with active subscriptions. 2) Data migration function to create UserSubscription records based on User model data. 3) Validation function to verify successful migration. 4) Rollback capability to revert changes if needed. 5) Progress tracking and reporting. Implement batched processing to handle large user bases without performance impact.", "testStrategy": "Test migration scripts on a copy of production data. Verify all users with subscriptions are correctly migrated. Test rollback functionality. Measure performance and optimize for large datasets. Create validation scripts to verify data integrity post-migration.", "priority": "medium", "dependencies": [30, 31, 32, 33], "status": "pending", "subtasks": []}, {"id": 44, "title": "Implement UserSubscription Record Creation for Existing Subscriptions", "description": "Create a system to automatically generate UserSubscription records for users with existing subscriptions during the migration process.", "details": "Implement a UserSubscriptionMigrator class with methods to: 1) identifyUsersForMigration(): User[] - Finds users with active subscriptions but no UserSubscription records. 2) migrateUser(userId: UUID): UserSubscription - Creates UserSubscription based on User data. 3) validateMigration(userId: UUID): boolean - Verifies successful migration. 4) batchMigrateUsers(batchSize: number): MigrationResult - Processes users in batches. Include detailed logging and error handling for the migration process.", "testStrategy": "Create unit tests for each migration function. Test with various user subscription scenarios. Implement integration tests that verify correct UserSubscription creation. Test batch processing with different batch sizes. Verify error handling and logging functionality.", "priority": "medium", "dependencies": [43], "status": "pending", "subtasks": []}, {"id": 45, "title": "Validate Data Integrity Between Old and New Systems", "description": "Create validation tools to ensure data integrity and consistency between legacy and new subscription systems.", "details": "Develop a DataIntegrityValidator with functions to: 1) validateUserSubscription(userId: UUID): ValidationResult - Checks consistency for a single user. 2) validateAllSubscriptions(): ValidationSummary - Checks all users with subscriptions. 3) generateInconsistencyReport(): Report - Creates detailed report of discrepancies. 4) automaticRepair(options: RepairOptions): RepairResult - Attempts to fix inconsistencies automatically. Include severity classification for different types of inconsistencies.", "testStrategy": "Create unit tests for validation logic with various consistency scenarios. Test with intentionally inconsistent data to verify detection. Implement integration tests for the repair functionality. Verify report generation with different types of inconsistencies.", "priority": "medium", "dependencies": [43, 44], "status": "pending", "subtasks": []}, {"id": 46, "title": "Create Rollback Procedures for Migration Safety", "description": "Implement rollback procedures to safely revert migration changes if issues are detected during the transition period.", "details": "Create a MigrationRollbackService with methods to: 1) createRollbackSnapshot(): SnapshotId - Captures current state before migration. 2) rollbackToSnapshot(snapshotId: SnapshotId): RollbackResult - Reverts to previous state. 3) validateRollbackSuccess(): boolean - Verifies successful rollback. 4) cleanupSnapshots(olderThan: Date): void - Removes old snapshots. Implement transaction-based rollback operations to ensure atomicity.", "testStrategy": "Create unit tests for snapshot creation and rollback logic. Test rollback scenarios with various types of changes. Implement integration tests that verify database state after rollback. Test performance with large datasets. Verify cleanup functionality.", "priority": "medium", "dependencies": [43, 44, 45], "status": "pending", "subtasks": []}, {"id": 47, "title": "Update PricingPage Component for New Subscription APIs", "description": "Modify the PricingPage component to support both legacy and new subscription APIs through feature flags.", "details": "Update the PricingPage component to: 1) Check feature flags to determine which subscription system to use. 2) Fetch subscription data from appropriate API endpoints. 3) Display consistent UI regardless of underlying system. 4) Handle subscription selection and checkout flow for both systems. 5) Implement proper error handling and loading states. Use dependency injection or adapter pattern to abstract the subscription system differences.", "testStrategy": "Create unit tests for the updated component with both feature flag states. Test UI rendering with various subscription data. Implement integration tests for the checkout flow. Test error handling and edge cases. Perform end-to-end tests with both subscription systems.", "priority": "medium", "dependencies": [39, 40], "status": "pending", "subtasks": []}, {"id": 48, "title": "Implement Feature Flags for Gradual Rollout", "description": "Create a feature flag system to control the gradual rollout of the new subscription system to users.", "details": "Implement a FeatureFlagService with methods to: 1) isFeatureEnabled(featureName: string, userId?: UUID): boolean - Checks if feature is enabled globally or for specific user. 2) enableFeatureForUser(featureName: string, userId: UUID): void - Enables feature for specific user. 3) enableFeatureGlobally(featureName: string): void - Enables feature for all users. 4) getFeaturePercentage(featureName: string): number - Gets rollout percentage. Create specific flags for 'newSubscriptionSystem', 'newCheckoutFlow', etc.", "testStrategy": "Create unit tests for feature flag logic with various scenarios. Test user-specific and global flag settings. Implement integration tests that verify feature-dependent behavior. Test performance with large user bases. Verify persistence of flag settings.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 49, "title": "Enhance Checkout Flow for New Subscription System", "description": "Update the checkout flow to work seamlessly with the new subscription system while maintaining backward compatibility.", "details": "Modify the checkout flow to: 1) Use feature flags to determine which subscription system to use. 2) For new system, include subscription metadata in checkout. 3) Handle post-checkout actions appropriately for both systems. 4) Implement proper error handling and recovery. 5) Provide consistent user experience regardless of underlying system. Update UI components to support both flows without visible differences to users.", "testStrategy": "Create unit tests for checkout logic with both subscription systems. Test the complete checkout flow with mock payment processors. Implement integration tests for post-checkout actions. Test error handling and recovery. Perform end-to-end tests with actual payment processors in test mode.", "priority": "high", "dependencies": [37, 47, 48], "status": "pending", "subtasks": []}, {"id": 50, "title": "Add Subscription Management UI Components", "description": "Create or enhance UI components for managing subscriptions that work with both legacy and new subscription systems.", "details": "Develop UI components for: 1) Subscription details display. 2) Upgrade/downgrade flow. 3) Cancellation process. 4) Subscription history view. 5) Payment method management. Ensure components work with both subscription systems based on feature flags. Implement responsive design and accessibility features. Use adapter pattern to abstract data source differences.", "testStrategy": "Create unit tests for all UI components with both subscription data formats. Test user interactions and state changes. Implement integration tests for data fetching and updates. Test accessibility compliance. Perform usability testing with different user scenarios.", "priority": "medium", "dependencies": [39, 47, 48], "status": "pending", "subtasks": []}, {"id": 51, "title": "Create Integration Tests for Webhook Handlers", "description": "Develop comprehensive integration tests for payment processor webhook handlers to ensure proper handling of subscription events.", "details": "Create integration test suite that: 1) Mocks webhook payloads for all relevant event types from Stripe and LemonSqueezy. 2) Verifies correct handling of each event type. 3) Tests both legacy and new subscription updates. 4) Validates database state after webhook processing. 5) Tests error handling and recovery. Include tests for edge cases like duplicate events, out-of-order events, and malformed payloads.", "testStrategy": "Use a test database for integration tests. Create fixtures with sample webhook payloads. Verify database state before and after webhook processing. Test with feature flags in different states. Measure performance and optimize webhook handling if needed.", "priority": "high", "dependencies": [35, 36, 38], "status": "pending", "subtasks": []}, {"id": 52, "title": "Implement API Integration Tests for Subscription Flows", "description": "Create integration tests for subscription-related API endpoints to ensure proper functionality with both subscription systems.", "details": "Develop API test suite that: 1) Tests all subscription endpoints with both legacy and new systems. 2) Verifies correct handling of different subscription operations (create, update, cancel, etc.). 3) Tests feature flag behavior. 4) Validates response formats and error handling. 5) Verifies database state after API operations. Include tests for authorization and permission checks.", "testStrategy": "Use a test database for integration tests. Create test users with various subscription states. Test API endpoints with different request parameters. Verify response formats and status codes. Test with feature flags in different states.", "priority": "high", "dependencies": [39, 40, 41], "status": "pending", "subtasks": []}, {"id": 53, "title": "Create Data Migration Validation Scripts", "description": "Develop scripts to validate the success and data integrity of the subscription data migration process.", "details": "Create validation scripts that: 1) Compare User model subscription data with UserSubscription records. 2) Identify inconsistencies or missing data. 3) Generate detailed reports of migration status. 4) Provide statistics on migration success rate. 5) Identify users requiring manual intervention. Implement both automated validation and tools for manual verification by administrators.", "testStrategy": "Test validation scripts with intentionally inconsistent data to verify detection. Run validation on test migrations before production. Measure performance with large datasets and optimize if needed. Verify report accuracy and completeness.", "priority": "medium", "dependencies": [43, 44, 45], "status": "pending", "subtasks": []}, {"id": 54, "title": "Test Payment Processor Integration End-to-End", "description": "Perform comprehensive end-to-end testing of the payment processor integration with both subscription systems.", "details": "Create end-to-end test suite that: 1) Tests complete subscription flows from checkout to webhook processing. 2) Verifies correct handling of all subscription events. 3) Tests with both Stripe and LemonSqueezy in test mode. 4) Validates database state after each step. 5) Tests both legacy and new subscription systems. Include tests for edge cases like failed payments, refunds, and subscription changes.", "testStrategy": "Use payment processor test environments. Create test users and subscriptions. Verify database state after each step in the flow. Test with feature flags in different states. Measure performance and identify bottlenecks.", "priority": "high", "dependencies": [37, 49, 51, 52], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-18T13:02:22.487Z", "updated": "2025-07-21T15:14:32.808Z", "description": "Tasks for master context"}}, "template-migration": {"tasks": [], "metadata": {"created": "2025-07-14T04:45:32.380Z", "updated": "2025-07-14T04:45:32.380Z", "description": "Tasks for migrating Wasp project to use the new admin dashboard template design"}}}
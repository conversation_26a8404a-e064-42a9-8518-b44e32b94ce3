# Backward Compatibility Guide

This guide explains how the backward compatibility layer works and how to use it during the transition from the legacy subscription system to the new UserSubscription system.

## Overview

The backward compatibility layer ensures that existing frontend code continues to work without modifications while the new subscription system is being rolled out. It provides:

- **Seamless API Compatibility**: Existing API endpoints return data in the expected format
- **Dual System Operation**: Both legacy and new systems can operate simultaneously
- **Feature Flags**: Control which system to use and how data is returned
- **Migration Support**: Tools to gradually migrate users and code

## Architecture

### Core Components

1. **SubscriptionCompatibilityService**: Main service for bridging legacy and new systems
2. **Legacy API Wrapper**: Provides legacy-compatible API endpoints
3. **Operation Enhancer**: Enhances existing operations with subscription features
4. **Feature Flags**: Controls system behavior during transition

### Data Flow

```
Frontend Request
       ↓
Legacy API Wrapper
       ↓
SubscriptionCompatibilityService
       ↓
[Legacy User Fields] + [New UserSubscription] → Enhanced Data
       ↓
Legacy-Compatible Response
```

## Usage Examples

### 1. Getting User Subscription Data

#### Legacy Format (Existing Code)
```typescript
// This continues to work without changes
const response = await fetch('/api/user/subscription');
const data = await response.json();

// Access legacy fields as before
console.log(data.subscriptionStatus);
console.log(data.subscriptionPlan);
console.log(data.credits);
```

#### Enhanced Format (New Code)
```typescript
// New code can access enhanced data
const response = await fetch('/api/user/subscription');
const data = await response.json();

// Access effective values (prioritizes new system)
console.log(data.effective.subscriptionStatus);
console.log(data.effective.hasActiveSubscription);

// Access new system data
console.log(data.subscription?.subscription.features);

// Check migration status
console.log(data.compatibility.isFullyMigrated);
```

### 2. Checking Subscription Access

#### Legacy Approach
```typescript
// Existing code continues to work
if (user.subscriptionStatus === 'active' && user.subscriptionPlan === 'pro') {
  // Allow premium features
}
```

#### Enhanced Approach
```typescript
import { checkOperationAccess } from '../subscriptions/operationEnhancer';

// New code can use enhanced access checking
const access = await checkOperationAccess(userId, 'premium_features', prisma);
if (access.hasAccess) {
  // Allow premium features
} else {
  console.log(access.reason); // Detailed reason for denial
}
```

### 3. Using Feature Flags

```typescript
import { SubscriptionFeatureFlags } from '../subscriptions/compatibilityLayer';

// Control system behavior
SubscriptionFeatureFlags.setFlag('enableDualSystemOperation', true);
SubscriptionFeatureFlags.setFlag('preferNewSystemData', false);

// Check current settings
if (SubscriptionFeatureFlags.shouldUseDualSystem()) {
  // Return both legacy and new data
}
```

## Migration Strategies

### Phase 1: Dual System Operation
- Enable both systems to run simultaneously
- All existing code continues to work
- New subscriptions create both legacy and new records
- Feature flag: `enableDualSystemOperation: true`

### Phase 2: Gradual Frontend Migration
- Update frontend components one by one to use new data
- Use enhanced API responses with both legacy and new data
- Feature flag: `preferNewSystemData: false` (still prioritize legacy)

### Phase 3: New System Priority
- Switch to prioritizing new system data
- Legacy data serves as fallback only
- Feature flag: `preferNewSystemData: true`

### Phase 4: Legacy System Removal
- Remove legacy subscription fields from User model
- Remove legacy API endpoints
- Feature flag: `useNewSubscriptionSystem: true`

## API Endpoints

### Legacy-Compatible Endpoints

#### GET /api/user/subscription/legacy
Returns subscription data in the exact legacy format:
```json
{
  "success": true,
  "data": {
    "subscriptionStatus": "active",
    "subscriptionPlan": "pro",
    "datePaid": "2024-01-15T10:00:00Z",
    "credits": 1000,
    "queues": 5,
    "hasActiveSubscription": true
  }
}
```

#### GET /api/user/subscription/check
Simple subscription status check:
```json
{
  "success": true,
  "data": {
    "hasActiveSubscription": true,
    "subscriptionStatus": "active",
    "subscriptionPlan": "pro"
  }
}
```

### Enhanced Endpoints

#### GET /api/user/subscription
Returns enhanced data with compatibility information:
```json
{
  "success": true,
  "data": {
    "credits": 1000,
    "queues": 5,
    "legacy": {
      "subscriptionStatus": "active",
      "subscriptionPlan": "pro",
      "datePaid": "2024-01-15T10:00:00Z"
    },
    "subscription": {
      "id": "uuid",
      "status": "active",
      "subscription": {
        "name": "Professional Plan",
        "features": ["unlimited_credits", "priority_support"]
      }
    },
    "effective": {
      "subscriptionStatus": "active",
      "subscriptionPlan": "Professional Plan",
      "hasActiveSubscription": true,
      "totalCreditsAllocated": 1000
    },
    "compatibility": {
      "hasNewSubscription": true,
      "hasLegacySubscription": true,
      "isFullyMigrated": true,
      "needsMigration": false
    }
  }
}
```

## Feature Access Control

### Predefined Features
- `basic_features`: Available to all plans
- `advanced_features`: Hobby and Pro plans
- `premium_features`: Pro plan only
- `unlimited_credits`: Pro plan only
- `priority_support`: Hobby and Pro plans
- `api_access`: Hobby and Pro plans
- `multiple_queues`: Hobby and Pro plans

### Usage Example
```typescript
import { checkOperationAccess } from '../subscriptions/operationEnhancer';

const access = await checkOperationAccess(userId, 'premium_features', prisma);
if (!access.hasAccess) {
  throw new Error(access.reason);
}
```

## Operation Enhancement

### Enhancing Existing Operations
```typescript
import { withSubscriptionCompatibility } from '../subscriptions/operationEnhancer';

// Enhance existing operation
const enhancedOperation = withSubscriptionCompatibility(originalOperation);

// The enhanced operation automatically adds subscription data to user objects
const result = await enhancedOperation(args, context);
// result.user now includes subscription compatibility data
```

### Adding Subscription Limits
```typescript
import { withSubscriptionLimits } from '../subscriptions/operationEnhancer';

const limitedOperation = withSubscriptionLimits(originalOperation, {
  requiredFeature: 'premium_features',
  creditsRequired: 10,
  queuesRequired: 2,
  operationName: 'Advanced Analysis'
});
```

## Middleware

### Legacy Subscription Data Middleware
```typescript
import { addLegacySubscriptionData } from '../subscriptions/legacyApiWrapper';

// Add to your route
app.use('/api/protected', addLegacySubscriptionData);
```

### Subscription Guard Middleware
```typescript
import { requireSubscription } from '../subscriptions/legacyApiWrapper';

// Protect routes with subscription requirements
app.get('/api/premium-feature', requireSubscription('pro'), handler);
```

## Best Practices

### 1. Gradual Migration
- Start with dual system operation
- Migrate frontend components incrementally
- Use feature flags to control rollout
- Monitor both systems during transition

### 2. Error Handling
- Always provide fallbacks for compatibility layer failures
- Log compatibility issues for debugging
- Gracefully degrade when new system is unavailable

### 3. Testing
- Test with users who have only legacy data
- Test with users who have only new data
- Test with users who have both systems
- Test feature flag transitions

### 4. Monitoring
- Monitor API response times during dual operation
- Track migration progress with compatibility flags
- Monitor error rates in compatibility layer

## Troubleshooting

### Common Issues

1. **User has legacy data but no UserSubscription**
   - Run migration script for the user
   - Check migration logs for errors

2. **Inconsistent subscription status**
   - Use validation script to check data consistency
   - Update both systems when making changes

3. **Feature access denied unexpectedly**
   - Check effective subscription status
   - Verify feature access rules
   - Check if subscription is active

### Debug Information
```typescript
const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(userId, prisma);
console.log('Debug info:', {
  hasLegacy: subscriptionData.hasLegacySubscription,
  hasNew: subscriptionData.hasNewSubscription,
  isFullyMigrated: subscriptionData.isFullyMigrated,
  effectiveStatus: SubscriptionCompatibilityService.getEffectiveSubscriptionStatus(subscriptionData),
  recommendations: SubscriptionCompatibilityService.getMigrationRecommendations(subscriptionData)
});
```

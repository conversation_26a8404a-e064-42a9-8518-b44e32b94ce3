/**
 * Validation utilities for Provider Mobile API
 * Using Zod schemas following Wasp patterns
 */

import { z } from 'zod';
import type { Request, Response } from 'express';
import { sendValidationError, formatZodErrors } from './responseUtils';

/**
 * Common validation schemas
 */

// ID validation
export const idSchema = z.number().int().positive('ID must be a positive integer');
export const stringIdSchema = z.string().regex(/^\d+$/, 'ID must be a numeric string').transform(Number);

// Pagination schemas
export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  skip: z.coerce.number().int().min(0).optional(),
  take: z.coerce.number().int().min(1).max(100).optional()
});

// Date validation
export const dateStringSchema = z.string().regex(
  /^\d{4}-\d{2}-\d{2} (T[0-2][0-9]:[0-5][0-9]:[0-5][0-9](\.[0-9]+)?)?$/,
  'Date must be in YYYY-MM-DD format'
);

export const dateTimeStringSchema = z.string().datetime({
  message: 'Must be a valid ISO 8601 date-time string'
}).transform(val => new Date(val));

// Phone number validation
export const phoneSchema = z.string().regex(
  /^\+?[1-9]\d{1,14}$/,
  'Phone number must be in international format'
).optional();

// Common string validations
export const titleSchema = z.string().min(1, 'Title is required').max(255, 'Title too long');
export const descriptionSchema = z.string().max(1000, 'Description too long').optional();
export const colorSchema = z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Color must be a valid hex color').optional();

// Provider Profile Validation Schemas
export const updateProviderProfileSchema = z.object({
  title: titleSchema.optional(),
  phone: phoneSchema,
  presentation: descriptionSchema,
  providerCategoryId: idSchema.optional()
});

// Opening Hours Validation Schema
export const openingHoursSchema = z.object({
  dayOfWeek: z.string().min(1, "Day of week is required"),
  isActive: z.boolean().default(true),
  hours: z.array(z.object({
    timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  })),
});

// Location Validation Schemas
export const createLocationSchema = z.object({
  name: titleSchema,
  shortName: z.string().max(50, 'Short name too long').optional(),
  address: z.string().max(500, 'Address too long').optional(),
  city: z.string().max(100, 'City name too long').optional(),
  mobile: phoneSchema,
  isMobileHidden: z.boolean().default(false),
  fax: z.string().max(50, 'Fax too long').optional(),
  floor: z.string().max(50, 'Floor too long').optional(),
  parking: z.boolean().default(false),
  elevator: z.boolean().default(false),
  handicapAccess: z.boolean().default(false),
  timezone: z.string().max(50, 'Timezone too long').optional(),
  // Address details
  country: z.string().max(100, 'Country name too long').default('Algeria'),
  postalCode: z.string().max(20, 'Postal code too long').optional(),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  // Opening hours
  openingHours: z.array(openingHoursSchema).optional()
});

export const updateLocationSchema = createLocationSchema.partial();

// Service Category Validation Schemas
export const createServiceCategorySchema = z.object({
  title: titleSchema
});

export const updateServiceCategorySchema = z.object({
  title: titleSchema.optional()
});

// Customer Validation Schemas
export const createCustomerSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  mobileNumber: z.string().min(1, 'Mobile number is required'),
  email: z.string().email('Invalid email format').optional(),
  nationalId: z.string().optional(),
  notes: z.string().optional()
});

export const updateCustomerSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  mobileNumber: z.string().min(1, 'Mobile number is required').optional(),
  email: z.string().email('Invalid email format').optional(),
  nationalId: z.string().optional(),
  notes: z.string().optional()
});

// Service Validation Schemas
export const createServiceSchema = z.object({
  title: titleSchema,
  duration: z.number().int().min(1, 'Duration must be at least 1 minute'),
  price: z.number().min(0, 'Price cannot be negative'),
  pointsRequirements: z.number().int().min(0, 'Points requirements cannot be negative').default(1),
  isPublic: z.boolean().default(true),
  deliveryType: z.enum(['at_location', 'at_customer', 'both']),
  servedRegions: z.array(z.string()).optional().nullable(),
  description: z.string().max(1000, 'Description too long').optional(),
  color: colorSchema,
  acceptOnline: z.boolean().default(true),
  acceptNew: z.boolean().default(true),
  notificationOn: z.boolean().default(true)
});

export const updateServiceSchema = z.object({
  title: titleSchema.optional(),
  duration: z.number().int().min(1, 'Duration must be at least 1 minute').optional(),
  price: z.number().min(0, 'Price cannot be negative').optional(),
  pointsRequirements: z.number().int().min(0, 'Points requirements cannot be negative').optional(),
  isPublic: z.boolean().optional(),
  deliveryType: z.enum(['at_location', 'at_customer', 'both']).optional(),
  servedRegions: z.array(z.string().min(1, 'Region name cannot be empty'))
    .optional()
    .nullable(),
  description: z.string().max(1000, 'Description too long').optional(),
  color: colorSchema,
  acceptOnline: z.boolean().optional(),
  acceptNew: z.boolean().optional(),
  notificationOn: z.boolean().optional()
});

// Queue Validation Schemas
export const createQueueSchema = z.object({
  title: titleSchema,
  sProvidingPlaceId: idSchema,
  isActive: z.boolean().default(true),
  serviceIds: z.array(idSchema).min(1, 'At least one service must be assigned'),
  openingHours: z.array(openingHoursSchema).optional()
});

export const updateQueueSchema = z.object({
  title: titleSchema.optional(),
  isActive: z.boolean().optional(),
  serviceIds: z.array(idSchema).min(1, 'At least one service must be assigned').optional(),
  openingHours: z.array(openingHoursSchema).optional()
});

// Queue Service Assignment Validation Schemas
export const assignServiceToQueueSchema = z.object({
  serviceId: idSchema
});

export const removeServiceFromQueueSchema = z.object({
  serviceId: idSchema
});

// Reschedule Management Validation Schemas
export const getReschedulesSchema = z.object({
  status: z.enum(['pending', 'accepted', 'rejected', 'all']).optional().default('pending'),
  from: dateStringSchema.optional(),
  to: dateStringSchema.optional(),
  page: z.string().transform(val => parseInt(val, 10)).optional().default('1'),
  limit: z.string().transform(val => parseInt(val, 10)).optional().default('20')
});

export const createRescheduleSchema = z.object({
  newStartTime: dateTimeStringSchema,
  reason: z.string().max(500, 'Reason too long').optional(),
  notifyCustomer: z.boolean().optional().default(true)
});

export const respondToRescheduleSchema = z.object({
  response: z.enum(['approve', 'reject']),
  notes: z.string().max(500, 'Notes too long').optional(),
  notifyCustomer: z.boolean().optional().default(true)
});

// Schedule Validation Schemas
const timeSlotSchema = z.object({
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)')
}).refine(data => data.startTime < data.endTime, {
  message: 'Start time must be before end time',
  path: ['endTime']
});

export const getSchedulesSchema = z.object({
  query: z.object({
    locationId: stringIdSchema.optional(),
    queueId: stringIdSchema.optional(),
    from: dateStringSchema.optional(),
    to: dateStringSchema.optional()
  })
});

export const createScheduleSchema = z.object({
  dayOfWeek: z.number().int().min(0).max(6, 'Day of week must be 0-6 (Sunday-Saturday)'),
  timeSlots: z.array(timeSlotSchema).optional().default([]),
  locationId: idSchema,
  isActive: z.boolean().optional().default(true),
  type: z.string().max(50).optional().default('regular'),
  effectiveFrom: dateStringSchema.optional(),
  effectiveTo: dateStringSchema.optional()
}).refine(data => {
  if (data.effectiveFrom && data.effectiveTo) {
    return new Date(data.effectiveFrom) < new Date(data.effectiveTo);
  }
  return true;
}, {
  message: 'Effective from date must be before effective to date',
  path: ['effectiveTo']
});

export const updateScheduleSchema = z.object({
  dayOfWeek: z.number().int().min(0).max(6, 'Day of week must be 0-6 (Sunday-Saturday)').optional(),
  timeSlots: z.array(timeSlotSchema).optional(),
  isActive: z.boolean().optional(),
  type: z.string().max(50).optional(),
  effectiveFrom: dateStringSchema.optional(),
  effectiveTo: dateStringSchema.optional()
}).refine(data => {
  if (data.effectiveFrom && data.effectiveTo) {
    return new Date(data.effectiveFrom) < new Date(data.effectiveTo);
  }
  return true;
}, {
  message: 'Effective from date must be before effective to date',
  path: ['effectiveTo']
});

// Appointment Validation Schemas
export const createAppointmentSchema = z.object({
  customerId: z.string().uuid('Invalid customer ID format'),
  serviceId: idSchema,
  queueId: idSchema,
  expectedStartTime: dateTimeStringSchema,
  notes: z.string().max(500, 'Notes too long').optional()
  // Note: placeId and endTime will be derived/calculated in the handler
});

export const updateAppointmentSchema = z.object({
  serviceId: idSchema,
  queueId: idSchema,
  expectedStartTime: dateTimeStringSchema,
  expectedEndTime: dateTimeStringSchema,
  notes: z.string().max(500, 'Notes too long').optional()
});

// Transform function to convert mobile API format to backend operation format
export function transformCreateAppointmentData(
  mobileData: z.infer<typeof createAppointmentSchema>,
  serviceDuration: number,
  placeId: number
): any {
  const startTime = new Date(mobileData.expectedStartTime);
  const endTime = new Date(startTime.getTime() + (serviceDuration * 60 * 1000)); // Add service duration in minutes

  return {
    customerUserId: mobileData.customerId,
    serviceId: mobileData.serviceId,
    placeId: placeId,
    queueId: mobileData.queueId,
    startTime: startTime,
    endTime: endTime,
    notes: mobileData.notes
  };
}

// Filter Validation Schemas
export const serviceFiltersSchema = paginationSchema.extend({
  isActive: z.coerce.boolean().optional(),
  categoryId: stringIdSchema.optional(),
  search: z.string().max(255).optional(),
  minDuration: z.coerce.number().int().min(1).optional(),
  maxDuration: z.coerce.number().int().min(1).optional()
});

export const appointmentFiltersSchema = paginationSchema.extend({
  status: z.enum(['pending', 'confirmed', 'InProgress', 'completed', 'canceled', 'noshow']).optional(),
  startDate: dateStringSchema.optional(),
  endDate: dateStringSchema.optional(),
  serviceId: stringIdSchema.optional(),
  queueId: stringIdSchema.optional()
});

export const locationFiltersSchema = paginationSchema.extend({
  search: z.string().max(255).optional(),
  city: z.string().max(100).optional()
});

/**
 * Validation middleware factory
 */
export function validateRequest<T extends z.ZodType>(
  schema: T,
  source: 'body' | 'query' | 'params' = 'body'
) {
  return (req: Request, res: Response, next: Function) => {
    const data = source === 'body' ? req.body : 
                 source === 'query' ? req.query : 
                 req.params;

    const result = schema.safeParse(data);
    
    if (!result.success) {
      const errors = formatZodErrors(result.error);
      return sendValidationError(res, errors, `Invalid ${source} data`);
    }

    // Attach validated data to request
    (req as any).validated = {
      ...(req as any).validated,
      [source]: result.data
    };

    next();
  };
}

/**
 * Validate and extract data from request
 */
export function validateAndExtract<T extends z.ZodType>(
  schema: T,
  data: unknown
): z.infer<T> {
  const result = schema.safeParse(data);
  
  if (!result.success) {
    throw new Error(`Validation failed: ${JSON.stringify(formatZodErrors(result.error))}`);
  }

  return result.data;
}

/**
 * Provider Customer API Handlers
 * Handles provider customer management endpoints
 */

import type { Request, Response } from 'express';
import { 
  sendSuccess, 
  sendError, 
  sendCreated,
  asyncHandler,
  type ProviderApiContext 
} from '../index';
import { 
  createCustomerSchema, 
  updateCustomerSchema,
  validateAndExtract 
} from '../utils/validationUtils';
import { 
  getProviderCustomers as getProviderCustomersOp,
  createProviderCustomer as createProviderCustomerOp,
  updateProviderCustomer as updateProviderCustomerOp
} from '../../operations';
import type { CustomerResponse, CreateCustomerRequest, UpdateCustomerRequest } from '../types';

/**
 * GET /api/providers/customers
 * Get all provider customers with search and pagination
 */
export const getProviderCustomers = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Extract query parameters
    const { search, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    // Call the operation to get customers
    const customerFolders = await getProviderCustomersOp(undefined, context);
    
    // Transform to response format
    const customers: CustomerResponse[] = customerFolders.map((folder: any) => ({
      id: folder.customer.id,
      firstName: folder.customer.firstName,
      lastName: folder.customer.lastName,
      mobileNumber: folder.customer.mobileNumber,
      email: folder.customer.email,
      nationalId: folder.customer.nationalId,
      notes: folder.notes,
      appointmentCount: folder.appointments?.length || 0,
      createdAt: folder.createdAt
    }));

    // Apply search filter if provided
    let filteredCustomers = customers;
    if (search && typeof search === 'string') {
      const searchLower = search.toLowerCase();
      filteredCustomers = customers.filter(customer => 
        customer.firstName.toLowerCase().includes(searchLower) ||
        customer.lastName.toLowerCase().includes(searchLower) ||
        customer.mobileNumber.includes(search) ||
        (customer.email && customer.email.toLowerCase().includes(searchLower))
      );
    }

    // Apply sorting
    filteredCustomers.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'name':
          aValue = `${a.firstName} ${a.lastName}`.toLowerCase();
          bValue = `${b.firstName} ${b.lastName}`.toLowerCase();
          break;
        case 'email':
          aValue = a.email?.toLowerCase() || '';
          bValue = b.email?.toLowerCase() || '';
          break;
        case 'createdAt':
        default:
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
      }
      
      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const pageNum = parseInt(page as string) || 1;
    const limitNum = parseInt(limit as string) || 20;
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedCustomers = filteredCustomers.slice(startIndex, endIndex);

    const response = {
      data: paginatedCustomers,
      pagination: {
        total: filteredCustomers.length,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(filteredCustomers.length / limitNum),
        hasNext: endIndex < filteredCustomers.length,
        hasPrev: pageNum > 1
      }
    };

    sendSuccess(res, response, 'Provider customers retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderCustomers] Error:', error);
    sendError(res, error, 'Failed to retrieve provider customers');
  }
});

/**
 * GET /api/providers/customers/:id
 * Get a single provider customer by ID
 */
export const getProviderCustomer = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get customer ID from URL parameters
    const customerId = req.params.id;
    if (!customerId) {
      return sendError(res, { statusCode: 400, message: 'Customer ID is required' });
    }

    // Get all customers and find the specific one
    const customerFolders = await getProviderCustomersOp(undefined, context);
    const customerFolder = customerFolders.find((folder: any) => folder.customer.id === customerId);

    if (!customerFolder) {
      return sendError(res, { statusCode: 404, message: 'Customer not found' });
    }

    // Transform to response format
    const customer: CustomerResponse = {
      id: customerFolder.customer.id,
      firstName: customerFolder.customer.firstName,
      lastName: customerFolder.customer.lastName,
      mobileNumber: customerFolder.customer.mobileNumber,
      email: customerFolder.customer.email,
      nationalId: customerFolder.customer.nationalId,
      notes: customerFolder.notes,
      appointmentCount: customerFolder.appointments?.length || 0,
      createdAt: customerFolder.createdAt
    };

    sendSuccess(res, customer, 'Provider customer retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderCustomer] Error:', error);
    sendError(res, error, 'Failed to retrieve provider customer');
  }
});

/**
 * POST /api/providers/customers
 * Create a new provider customer
 */
export const createProviderCustomer = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const customerData = validateAndExtract(createCustomerSchema, req.body);
    
    // Call the operation to create the customer
    const newCustomerFolder = await createProviderCustomerOp(customerData, context);
    
    // Get the created customer details by fetching updated customer list
    const customerFolders = await getProviderCustomersOp(undefined, context);
    const createdCustomerFolder = customerFolders.find((folder: any) => folder.id === newCustomerFolder.id);
    
    if (!createdCustomerFolder) {
      return sendError(res, { statusCode: 500, message: 'Failed to retrieve created customer' });
    }

    // Transform to response format
    const response: CustomerResponse = {
      id: createdCustomerFolder.customer.id,
      firstName: createdCustomerFolder.customer.firstName,
      lastName: createdCustomerFolder.customer.lastName,
      mobileNumber: createdCustomerFolder.customer.mobileNumber,
      email: createdCustomerFolder.customer.email,
      nationalId: createdCustomerFolder.customer.nationalId,
      notes: createdCustomerFolder.notes,
      appointmentCount: 0, // New customer has no appointments
      createdAt: createdCustomerFolder.createdAt
    };

    sendCreated(res, response, 'Provider customer created successfully');
  } catch (error: any) {
    console.error('[createProviderCustomer] Error:', error);
    sendError(res, error, 'Failed to create provider customer');
  }
});

/**
 * PUT /api/providers/customers/:id
 * Update a provider customer
 */
export const updateProviderCustomer = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get customer ID from URL parameters
    const customerId = req.params.id;
    if (!customerId) {
      return sendError(res, { statusCode: 400, message: 'Customer ID is required' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateCustomerSchema, req.body);
    
    // Call the operation to update the customer
    const updatedCustomerFolder = await updateProviderCustomerOp({
      customerUserId: customerId,
      ...updateData
    }, context);
    
    // Transform to response format
    const response: CustomerResponse = {
      id: updatedCustomerFolder.customer.id,
      firstName: updatedCustomerFolder.customer.firstName,
      lastName: updatedCustomerFolder.customer.lastName,
      mobileNumber: updatedCustomerFolder.customer.mobileNumber,
      email: updatedCustomerFolder.customer.email,
      nationalId: updatedCustomerFolder.customer.nationalId,
      notes: updatedCustomerFolder.notes,
      appointmentCount: 0, // Would need separate query to get appointment count
      createdAt: updatedCustomerFolder.createdAt
    };

    sendSuccess(res, response, 'Provider customer updated successfully');
  } catch (error: any) {
    console.error('[updateProviderCustomer] Error:', error);
    sendError(res, error, 'Failed to update provider customer');
  }
});

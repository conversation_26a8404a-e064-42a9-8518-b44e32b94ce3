/**
 * Provider Queue API Handlers
 * Handles provider queue/resource management endpoints
 */

import type { Request, Response } from 'express';
import {
  sendSuccess,
  sendError,
  sendCreated,
  asyncHandler,
  type ProviderApiContext
} from '../index';
import { createQueueSchema, updateQueueSchema, validateAndExtract } from '../utils/validationUtils';
import {
  createProviderQueue as createProviderQueueOp,
  getProviderQueues as getProviderQueuesOp,
  getQueuesByLocation as getQueuesByLocationOp,
  updateProviderQueue as updateProviderQueueOp,
  deleteProviderQueue as deleteProviderQueueOp
} from '../../operations';
import type { QueueResponse, CreateQueueRequest, UpdateQueueRequest } from '../types';

/**
 * GET /api/providers/queues
 * Get all provider queues
 */
export const getProviderQueues = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Call the operation to get all provider queues
    const queues = await getProviderQueuesOp({}, context);

    // Transform to response format
    const response: QueueResponse[] = queues.map(queue => ({
      id: queue.id,
      title: queue.title,
      isActive: queue.isActive,
      sProvidingPlaceId: queue.sProvidingPlaceId,
      services: (queue as any).services || [], // Include services if requested
      openingHours: (queue as any).openings?.map((opening: any) => ({
        dayOfWeek: opening.dayOfWeek,
        isActive: opening.isActive,
        hours: opening.hours.map((hour: any) => ({
          timeFrom: hour.timeFrom,
          timeTo: hour.timeTo
        }))
      })) || []
    }));

    sendSuccess(res, response, 'Provider queues retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderQueues] Error:', error);
    sendError(res, error, 'Failed to retrieve provider queues');
  }
});

/**
 * POST /api/providers/queues
 * Create a new provider queue
 */
export const createProviderQueue = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const queueData = validateAndExtract(createQueueSchema, req.body);

    // Call the operation to create the queue
    const newQueue = await createProviderQueueOp(queueData, context);

    // Transform to response format
    const response: QueueResponse = {
      id: newQueue.id,
      title: newQueue.title,
      isActive: newQueue.isActive,
      sProvidingPlaceId: newQueue.sProvidingPlaceId,
      services: [], // Services will be populated separately if needed
      openingHours: (newQueue as any).openings?.map((opening: any) => ({
        dayOfWeek: opening.dayOfWeek,
        isActive: opening.isActive,
        hours: opening.hours.map((hour: any) => ({
          timeFrom: hour.timeFrom,
          timeTo: hour.timeTo
        }))
      })) || []
    };

    sendCreated(res, response, 'Provider queue created successfully');
  } catch (error: any) {
    console.error('[createProviderQueue] Error:', error);
    sendError(res, error, 'Failed to create provider queue');
  }
});

/**
 * PUT /api/providers/queues/:id
 * Update a provider queue
 */
export const updateProviderQueue = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get queue ID from URL parameters
    const queueId = parseInt(req.params.id);
    if (!queueId || isNaN(queueId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid queue ID' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateQueueSchema, req.body);
    console.log('updateData', updateData);
    const obj = {
      queueId,
      ...updateData,
      serviceIds: updateData.serviceIds
    };
    console.log('obj', obj);

    // Call the operation to update the queue
    const updatedQueue: any = await updateProviderQueueOp(obj, context);

    // Transform to response format
    const response: QueueResponse = {
      id: updatedQueue.id,
      title: updatedQueue.title,
      isActive: updatedQueue.isActive,
      sProvidingPlaceId: updatedQueue.sProvidingPlaceId,
      services: (updatedQueue as any).services?.map((service: any) => ({
        id: service.id,
        title: service.title
      })) || [],
      openingHours: (updatedQueue as any).openings?.map((opening: any) => ({
        dayOfWeek: opening.dayOfWeek,
        isActive: opening.isActive,
        hours: opening.hours.map((hour: any) => ({
          timeFrom: hour.timeFrom,
          timeTo: hour.timeTo
        }))
      })) || []
    };

    sendSuccess(res, response, 'Provider queue updated successfully');
  } catch (error: any) {
    console.error('[updateProviderQueue] Error:', error);
    sendError(res, error, 'Failed to update provider queue');
  }
});

/**
 * DELETE /api/providers/queues/:id
 * Delete a provider queue
 */
export const deleteProviderQueue = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get queue ID from URL parameters
    const queueId = parseInt(req.params.id);
    if (!queueId || isNaN(queueId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid queue ID' });
    }

    // Call the operation to delete the queue
    const result = await deleteProviderQueueOp({
      queueId
    }, context);

    sendSuccess(res, { id: queueId }, result.message);
  } catch (error: any) {
    console.error('[deleteProviderQueue] Error:', error);
    sendError(res, error, 'Failed to delete provider queue');
  }
});

/**
 * GET /api/providers/locations/:locationId/queues
 * Get queues by location with optional filtering
 */
export const getQueuesByLocation = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get location ID from URL parameters
    const locationId = parseInt(req.params.locationId);
    if (!locationId || isNaN(locationId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid location ID' });
    }

    // Parse query parameters
    const isActive = req.query.isActive ? req.query.isActive === 'true' : undefined;
    const search = req.query.search as string || undefined;
    const includeServices = req.query.includeServices === 'true';

    // Call the operation to get queues by location
    const queues = await getQueuesByLocationOp({
      locationId,
      isActive,
      search,
      includeServices
    }, context);

    // Transform to response format
    const response: QueueResponse[] = queues.map(queue => ({
      id: queue.id,
      title: queue.title,
      isActive: queue.isActive,
      sProvidingPlaceId: queue.sProvidingPlaceId,
      services: (queue as any).services || [], // Include services if requested
      openingHours: (queue as any).openings?.map((opening: any) => ({
        dayOfWeek: opening.dayOfWeek,
        isActive: opening.isActive,
        hours: opening.hours.map((hour: any) => ({
          timeFrom: hour.timeFrom,
          timeTo: hour.timeTo
        }))
      })) || []
    }));

    sendSuccess(res, response, 'Queues retrieved successfully');
  } catch (error: any) {
    console.error('[getQueuesByLocation] Error:', error);
    sendError(res, error, 'Failed to retrieve queues by location');
  }
});

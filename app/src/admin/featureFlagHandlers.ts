import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';
import { FeatureFlagService, FeatureFlag } from '../shared/featureFlags';

// Validation schemas
const FeatureFlagSchema = z.nativeEnum(FeatureFlag);

const UpdateFeatureFlagSchema = z.object({
  featureName: FeatureFlagSchema,
  enabled: z.boolean().optional(),
  percentage: z.number().min(0).max(100).optional(),
});

const UserOverrideSchema = z.object({
  featureName: FeatureFlagSchema,
  userId: z.string().uuid(),
  enabled: z.boolean(),
});

// --- Handler for Getting All Feature Flags (Admin) ---
export const handleGetFeatureFlags = async (req: Request, res: Response, context: any) => {
  try {
    // Check if user is admin
    if (!context.user || context.user.role !== 'ADMIN') {
      throw new HttpError(403, 'Admin access required');
    }

    const features = FeatureFlagService.getAllFeatures();

    return res.status(200).json({
      success: true,
      message: 'Feature flags retrieved successfully',
      data: {
        features,
      },
    });
  } catch (error: any) {
    console.error('[API] Error fetching feature flags:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to fetch feature flags';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

// --- Handler for Updating Feature Flag Configuration (Admin) ---
export const handleUpdateFeatureFlag = async (req: Request, res: Response, context: any) => {
  try {
    // Check if user is admin
    if (!context.user || context.user.role !== 'ADMIN') {
      throw new HttpError(403, 'Admin access required');
    }

    // Validate request body
    const parsedBody = UpdateFeatureFlagSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { featureName, enabled, percentage } = parsedBody.data;

    // Update feature flag configuration
    if (enabled !== undefined) {
      if (enabled) {
        FeatureFlagService.enableFeatureGlobally(featureName);
      } else {
        FeatureFlagService.disableFeatureGlobally(featureName);
      }
    }

    if (percentage !== undefined) {
      FeatureFlagService.setFeaturePercentage(featureName, percentage);
    }

    // Get updated feature configuration
    const features = FeatureFlagService.getAllFeatures();
    const updatedFeature = features.find(f => f.name === featureName);

    return res.status(200).json({
      success: true,
      message: 'Feature flag updated successfully',
      data: {
        feature: updatedFeature,
      },
    });
  } catch (error: any) {
    console.error('[API] Error updating feature flag:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to update feature flag';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

// --- Handler for Managing User Overrides (Admin) ---
export const handleUpdateUserOverride = async (req: Request, res: Response, context: any) => {
  try {
    // Check if user is admin
    if (!context.user || context.user.role !== 'ADMIN') {
      throw new HttpError(403, 'Admin access required');
    }

    // Validate request body
    const parsedBody = UserOverrideSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { featureName, userId, enabled } = parsedBody.data;

    // Verify that the target user exists
    const targetUser = await context.entities.User.findUnique({
      where: { id: userId },
      select: { id: true, email: true },
    });

    if (!targetUser) {
      throw new HttpError(404, 'Target user not found');
    }

    // Update user override
    if (enabled) {
      FeatureFlagService.enableFeatureForUser(featureName, userId);
    } else {
      FeatureFlagService.disableFeatureForUser(featureName, userId);
    }

    // Check current status for the user
    const isEnabled = FeatureFlagService.isFeatureEnabled(featureName, userId);
    const hasOverride = FeatureFlagService.hasUserOverride(featureName, userId);

    return res.status(200).json({
      success: true,
      message: 'User override updated successfully',
      data: {
        featureName,
        userId,
        userEmail: targetUser.email,
        isEnabled,
        hasOverride,
      },
    });
  } catch (error: any) {
    console.error('[API] Error updating user override:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to update user override';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

// --- Handler for Checking Feature Status for Current User ---
export const handleGetUserFeatureStatus = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;
    const featureName = req.params.featureName as FeatureFlag;

    // Validate feature name
    const parsedFeatureName = FeatureFlagSchema.safeParse(featureName);
    if (!parsedFeatureName.success) {
      throw new HttpError(400, 'Invalid feature name');
    }

    const isEnabled = FeatureFlagService.isFeatureEnabled(parsedFeatureName.data, userId);
    const hasOverride = FeatureFlagService.hasUserOverride(parsedFeatureName.data, userId);
    const globalPercentage = FeatureFlagService.getFeaturePercentage(parsedFeatureName.data);

    return res.status(200).json({
      success: true,
      message: 'Feature status retrieved successfully',
      data: {
        featureName: parsedFeatureName.data,
        isEnabled,
        hasOverride,
        globalPercentage,
      },
    });
  } catch (error: any) {
    console.error('[API] Error getting user feature status:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to get feature status';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

// --- Handler for Getting All Feature Statuses for Current User ---
export const handleGetAllUserFeatureStatuses = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;
    const features = FeatureFlagService.getAllFeatures();

    const userFeatureStatuses = features.map(feature => ({
      featureName: feature.name,
      isEnabled: FeatureFlagService.isFeatureEnabled(feature.name, userId),
      hasOverride: FeatureFlagService.hasUserOverride(feature.name, userId),
      globalEnabled: feature.enabled,
      globalPercentage: feature.percentage,
      description: feature.description,
    }));

    return res.status(200).json({
      success: true,
      message: 'User feature statuses retrieved successfully',
      data: {
        userId,
        features: userFeatureStatuses,
      },
    });
  } catch (error: any) {
    console.error('[API] Error getting user feature statuses:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to get user feature statuses';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

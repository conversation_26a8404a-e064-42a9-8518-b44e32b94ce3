import { type PrismaClient } from '@prisma/client';

/**
 * Utilities for validating credit allocation and preventing double allocation
 */
export class CreditValidationUtils {
  
  /**
   * Check if credits have already been allocated for a specific payment
   */
  static async hasCreditsBeenAllocated(
    {
      userId,
      paymentProcessorSubscriptionId,
    }: {
      userId: string;
      paymentProcessorSubscriptionId: string;
    },
    prismaClient: PrismaClient
  ): Promise<boolean> {
    try {
      const existingAllocation = await prismaClient.userSubscription.findFirst({
        where: {
          userId,
          paymentProcessorSubscriptionId,
          creditsAllocated: { gt: 0 },
        },
      });

      return !!existingAllocation;
    } catch (error) {
      console.error('Error checking credit allocation:', error);
      return false; // Assume not allocated if check fails
    }
  }

  /**
   * Get duplicate payment processor subscription IDs that might indicate double allocation
   */
  static async findDuplicateAllocations(
    prismaClient: PrismaClient
  ): Promise<Array<{
    paymentProcessorSubscriptionId: string;
    count: number;
    totalCredits: number;
    userIds: string[];
  }>> {
    try {
      const duplicates = await prismaClient.userSubscription.groupBy({
        by: ['paymentProcessorSubscriptionId'],
        where: {
          paymentProcessorSubscriptionId: { not: null },
          creditsAllocated: { gt: 0 },
        },
        _count: {
          id: true,
        },
        _sum: {
          creditsAllocated: true,
        },
        having: {
          id: {
            _count: {
              gt: 1,
            },
          },
        },
      });

      const result: Array<{
        paymentProcessorSubscriptionId: string;
        count: number;
        totalCredits: number;
        userIds: string[];
      }> = [];
      for (const duplicate of duplicates) {
        const userSubscriptions = await prismaClient.userSubscription.findMany({
          where: {
            paymentProcessorSubscriptionId: duplicate.paymentProcessorSubscriptionId,
            creditsAllocated: { gt: 0 },
          },
          select: { userId: true },
        });

        result.push({
          paymentProcessorSubscriptionId: duplicate.paymentProcessorSubscriptionId!,
          count: duplicate._count.id,
          totalCredits: duplicate._sum.creditsAllocated || 0,
          userIds: userSubscriptions.map(sub => sub.userId),
        });
      }

      return result;
    } catch (error) {
      console.error('Error finding duplicate allocations:', error);
      return [];
    }
  }

  /**
   * Validate credit consistency across all users
   */
  static async validateAllUserCredits(
    prismaClient: PrismaClient,
    limit = 100
  ): Promise<{
    totalUsers: number;
    inconsistentUsers: Array<{
      userId: string;
      userCredits: number;
      allocatedCredits: number;
      difference: number;
    }>;
  }> {
    try {
      const users = await prismaClient.user.findMany({
        select: { id: true, credits: true },
        take: limit,
      });

      const inconsistentUsers: Array<{
        userId: string;
        userCredits: number;
        allocatedCredits: number;
        difference: number;
      }> = [];

      for (const user of users) {
        const userSubscriptions = await prismaClient.userSubscription.findMany({
          where: { userId: user.id },
          select: { creditsAllocated: true },
        });

        const allocatedCredits = userSubscriptions.reduce(
          (sum, sub) => sum + sub.creditsAllocated, 
          0
        );

        // Allow some tolerance for credit usage
        const difference = allocatedCredits - user.credits;
        if (difference > 1000) { // Significant inconsistency threshold
          inconsistentUsers.push({
            userId: user.id,
            userCredits: user.credits,
            allocatedCredits,
            difference,
          });
        }
      }

      return {
        totalUsers: users.length,
        inconsistentUsers,
      };
    } catch (error) {
      console.error('Error validating user credits:', error);
      return {
        totalUsers: 0,
        inconsistentUsers: [],
      };
    }
  }

  /**
   * Generate a credit allocation report for monitoring
   */
  static async generateCreditReport(
    prismaClient: PrismaClient
  ): Promise<{
    totalUsers: number;
    totalCreditsInSystem: number;
    totalAllocatedCredits: number;
    activeSubscriptions: number;
    duplicateAllocations: number;
    inconsistentUsers: number;
    recentAllocations: Array<{
      userId: string;
      credits: number;
      date: Date;
      paymentProcessorSubscriptionId: string | null;
    }>;
  }> {
    try {
      // Get total users and credits
      const userStats = await prismaClient.user.aggregate({
        _count: { id: true },
        _sum: { credits: true },
      });

      // Get total allocated credits
      const allocationStats = await prismaClient.userSubscription.aggregate({
        _sum: { creditsAllocated: true },
        _count: { id: true },
      });

      // Get active subscriptions
      const activeSubscriptions = await prismaClient.userSubscription.count({
        where: { status: 'active' },
      });

      // Get duplicate allocations
      const duplicates = await this.findDuplicateAllocations(prismaClient);

      // Get recent allocations (last 24 hours)
      const recentAllocations = await prismaClient.userSubscription.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
          },
          creditsAllocated: { gt: 0 },
        },
        select: {
          userId: true,
          creditsAllocated: true,
          createdAt: true,
          paymentProcessorSubscriptionId: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 50,
      });

      // Quick validation of inconsistent users (sample)
      const validation = await this.validateAllUserCredits(prismaClient, 50);

      return {
        totalUsers: userStats._count.id,
        totalCreditsInSystem: userStats._sum.credits || 0,
        totalAllocatedCredits: allocationStats._sum.creditsAllocated || 0,
        activeSubscriptions,
        duplicateAllocations: duplicates.length,
        inconsistentUsers: validation.inconsistentUsers.length,
        recentAllocations: recentAllocations.map(allocation => ({
          userId: allocation.userId,
          credits: allocation.creditsAllocated,
          date: allocation.createdAt,
          paymentProcessorSubscriptionId: allocation.paymentProcessorSubscriptionId,
        })),
      };
    } catch (error) {
      console.error('Error generating credit report:', error);
      return {
        totalUsers: 0,
        totalCreditsInSystem: 0,
        totalAllocatedCredits: 0,
        activeSubscriptions: 0,
        duplicateAllocations: 0,
        inconsistentUsers: 0,
        recentAllocations: [],
      };
    }
  }

  /**
   * Fix duplicate credit allocations by removing duplicates and keeping the first one
   */
  static async fixDuplicateAllocations(
    prismaClient: PrismaClient,
    dryRun = true
  ): Promise<{
    duplicatesFound: number;
    duplicatesFixed: number;
    creditsRecovered: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    let duplicatesFixed = 0;
    let creditsRecovered = 0;

    try {
      const duplicates = await this.findDuplicateAllocations(prismaClient);
      
      if (dryRun) {
        console.log(`[DRY RUN] Found ${duplicates.length} duplicate allocations`);
        return {
          duplicatesFound: duplicates.length,
          duplicatesFixed: 0,
          creditsRecovered: 0,
          errors: [],
        };
      }

      for (const duplicate of duplicates) {
        try {
          const userSubscriptions = await prismaClient.userSubscription.findMany({
            where: {
              paymentProcessorSubscriptionId: duplicate.paymentProcessorSubscriptionId,
              creditsAllocated: { gt: 0 },
            },
            orderBy: { createdAt: 'asc' }, // Keep the first one
          });

          if (userSubscriptions.length > 1) {
            // Keep the first subscription, remove the rest
            const toRemove = userSubscriptions.slice(1);
            
            for (const subscription of toRemove) {
              await prismaClient.userSubscription.delete({
                where: { id: subscription.id },
              });
              
              // Deduct the duplicate credits from the user
              await prismaClient.user.update({
                where: { id: subscription.userId },
                data: {
                  credits: { decrement: subscription.creditsAllocated },
                },
              });

              creditsRecovered += subscription.creditsAllocated;
              duplicatesFixed++;
            }
          }
        } catch (error) {
          const errorMsg = `Failed to fix duplicate for ${duplicate.paymentProcessorSubscriptionId}: ${error}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      return {
        duplicatesFound: duplicates.length,
        duplicatesFixed,
        creditsRecovered,
        errors,
      };
    } catch (error) {
      const errorMsg = `Failed to fix duplicate allocations: ${error}`;
      console.error(errorMsg);
      return {
        duplicatesFound: 0,
        duplicatesFixed: 0,
        creditsRecovered: 0,
        errors: [errorMsg],
      };
    }
  }
}

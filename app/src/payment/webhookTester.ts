import { type PrismaClient } from '@prisma/client';
import { PaymentPlanId } from './plans';
import { SubscriptionMappingService } from './subscriptionMapping';

/**
 * Webhook Testing Utility
 * 
 * This utility helps test webhook handlers and validate UserSubscription creation
 * during development and testing phases.
 */

export interface WebhookTestResult {
  success: boolean;
  message: string;
  userSubscriptionId?: string;
  userCredits?: number;
  errors?: string[];
}

export class WebhookTester {
  
  /**
   * Test Stripe payment intent webhook simulation
   */
  static async testStripePaymentIntent(
    {
      userId,
      paymentPlanId,
      creditsAmount,
      paymentIntentId,
    }: {
      userId: string;
      paymentPlanId: PaymentPlanId;
      creditsAmount: number;
      paymentIntentId?: string;
    },
    prisma: PrismaClient
  ): Promise<WebhookTestResult> {
    try {
      // Initialize SubscriptionMappingService if needed
      if (!SubscriptionMappingService.isInitialized()) {
        await SubscriptionMappingService.initialize(prisma);
      }

      const subscriptionId = SubscriptionMappingService.getSubscriptionId(paymentPlanId);
      if (!subscriptionId) {
        return {
          success: false,
          message: `No subscription mapping found for plan ${paymentPlanId}`,
        };
      }

      const testPaymentIntentId = paymentIntentId || `pi_test_${Date.now()}`;

      // Simulate the webhook handler logic
      const result = await prisma.$transaction(async (tx) => {
        // Check for existing UserSubscription
        const existingUserSubscription = await tx.userSubscription.findFirst({
          where: {
            userId,
            paymentProcessorSubscriptionId: testPaymentIntentId,
          },
        });

        if (existingUserSubscription) {
          return {
            success: false,
            message: `UserSubscription already exists for payment ${testPaymentIntentId}`,
          };
        }

        // Update User model (legacy compatibility)
        const updatedUser = await tx.user.update({
          where: { id: userId },
          data: {
            credits: { increment: creditsAmount },
            datePaid: new Date(),
          },
        });

        // Create UserSubscription record
        const userSubscription = await tx.userSubscription.create({
          data: {
            userId,
            subscriptionId,
            status: 'active',
            startDate: new Date(),
            endDate: null, // One-time purchases don't expire
            creditsAllocated: creditsAmount,
            paymentProcessorSubscriptionId: testPaymentIntentId,
          },
        });

        return {
          success: true,
          message: `Test payment intent processed successfully`,
          userSubscriptionId: userSubscription.id,
          userCredits: updatedUser.credits,
        };
      });

      return result;
    } catch (error) {
      return {
        success: false,
        message: `Test failed: ${error}`,
        errors: [String(error)],
      };
    }
  }

  /**
   * Test LemonSqueezy order webhook simulation
   */
  static async testLemonSqueezyOrder(
    {
      userId,
      paymentPlanId,
      creditsAmount,
      orderNumber,
    }: {
      userId: string;
      paymentPlanId: PaymentPlanId;
      creditsAmount: number;
      orderNumber?: string;
    },
    prisma: PrismaClient
  ): Promise<WebhookTestResult> {
    try {
      // Initialize SubscriptionMappingService if needed
      if (!SubscriptionMappingService.isInitialized()) {
        await SubscriptionMappingService.initialize(prisma);
      }

      const subscriptionId = SubscriptionMappingService.getSubscriptionId(paymentPlanId);
      if (!subscriptionId) {
        return {
          success: false,
          message: `No subscription mapping found for plan ${paymentPlanId}`,
        };
      }

      const testOrderNumber = orderNumber || `order_test_${Date.now()}`;

      // Simulate the webhook handler logic
      const result = await prisma.$transaction(async (tx) => {
        // Check for existing UserSubscription
        const existingUserSubscription = await tx.userSubscription.findFirst({
          where: {
            userId,
            paymentProcessorSubscriptionId: testOrderNumber,
          },
        });

        if (existingUserSubscription) {
          return {
            success: false,
            message: `UserSubscription already exists for order ${testOrderNumber}`,
          };
        }

        // Update User model (legacy compatibility)
        const updatedUser = await tx.user.update({
          where: { id: userId },
          data: {
            credits: { increment: creditsAmount },
            datePaid: new Date(),
          },
        });

        // Create UserSubscription record
        const userSubscription = await tx.userSubscription.create({
          data: {
            userId,
            subscriptionId,
            status: 'active',
            startDate: new Date(),
            endDate: null, // One-time purchases don't expire
            creditsAllocated: creditsAmount,
            paymentProcessorSubscriptionId: testOrderNumber,
          },
        });

        return {
          success: true,
          message: `Test LemonSqueezy order processed successfully`,
          userSubscriptionId: userSubscription.id,
          userCredits: updatedUser.credits,
        };
      });

      return result;
    } catch (error) {
      return {
        success: false,
        message: `Test failed: ${error}`,
        errors: [String(error)],
      };
    }
  }

  /**
   * Test subscription webhook simulation
   */
  static async testSubscriptionWebhook(
    {
      userId,
      paymentPlanId,
      subscriptionStatus,
      subscriptionId: externalSubscriptionId,
    }: {
      userId: string;
      paymentPlanId: PaymentPlanId;
      subscriptionStatus: 'active' | 'cancelled' | 'expired';
      subscriptionId?: string;
    },
    prisma: PrismaClient
  ): Promise<WebhookTestResult> {
    try {
      // Initialize SubscriptionMappingService if needed
      if (!SubscriptionMappingService.isInitialized()) {
        await SubscriptionMappingService.initialize(prisma);
      }

      const subscriptionId = SubscriptionMappingService.getSubscriptionId(paymentPlanId);
      if (!subscriptionId) {
        return {
          success: false,
          message: `No subscription mapping found for plan ${paymentPlanId}`,
        };
      }

      const testExternalSubscriptionId = externalSubscriptionId || `sub_test_${Date.now()}`;

      // Simulate the webhook handler logic
      const result = await prisma.$transaction(async (tx) => {
        // Update User model (legacy compatibility)
        const updatedUser = await tx.user.update({
          where: { id: userId },
          data: {
            subscriptionPlan: paymentPlanId,
            subscriptionStatus,
          },
        });

        // Update or create UserSubscription record
        const existingUserSubscription = await tx.userSubscription.findFirst({
          where: {
            paymentProcessorSubscriptionId: testExternalSubscriptionId,
          },
        });

        let userSubscription;
        if (existingUserSubscription) {
          // Update existing subscription
          userSubscription = await tx.userSubscription.update({
            where: { id: existingUserSubscription.id },
            data: {
              status: subscriptionStatus,
            },
          });
        } else {
          // Create new subscription record
          const startDate = new Date();
          const endDate = new Date();
          endDate.setDate(endDate.getDate() + 30); // 30 days from now

          userSubscription = await tx.userSubscription.create({
            data: {
              userId,
              subscriptionId,
              status: subscriptionStatus,
              startDate,
              endDate,
              creditsAllocated: 0, // Subscriptions don't allocate credits directly
              paymentProcessorSubscriptionId: testExternalSubscriptionId,
            },
          });
        }

        return {
          success: true,
          message: `Test subscription webhook processed successfully`,
          userSubscriptionId: userSubscription.id,
          userCredits: updatedUser.credits,
        };
      });

      return result;
    } catch (error) {
      return {
        success: false,
        message: `Test failed: ${error}`,
        errors: [String(error)],
      };
    }
  }

  /**
   * Validate webhook processing results
   */
  static async validateWebhookResults(
    userId: string,
    expectedCredits: number,
    prisma: PrismaClient
  ): Promise<{ isValid: boolean; issues: string[] }> {
    const issues: string[] = [];

    try {
      // Check user credits
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { credits: true },
      });

      if (!user) {
        issues.push(`User ${userId} not found`);
        return { isValid: false, issues };
      }

      if (user.credits !== expectedCredits) {
        issues.push(`Expected ${expectedCredits} credits, but user has ${user.credits}`);
      }

      // Check UserSubscription records
      const userSubscriptions = await prisma.userSubscription.findMany({
        where: { userId },
        include: { subscription: true },
      });

      if (userSubscriptions.length === 0) {
        issues.push(`No UserSubscription records found for user ${userId}`);
      }

      // Check for duplicate allocations
      const duplicateAllocations = userSubscriptions.filter((sub, index, arr) => 
        arr.findIndex(s => s.paymentProcessorSubscriptionId === sub.paymentProcessorSubscriptionId) !== index
      );

      if (duplicateAllocations.length > 0) {
        issues.push(`Duplicate UserSubscription records found: ${duplicateAllocations.map(d => d.paymentProcessorSubscriptionId).join(', ')}`);
      }

      return { isValid: issues.length === 0, issues };
    } catch (error) {
      issues.push(`Validation failed: ${error}`);
      return { isValid: false, issues };
    }
  }

  /**
   * Clean up test data
   */
  static async cleanupTestData(
    userId: string,
    testIdentifiers: string[],
    prisma: PrismaClient
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Remove test UserSubscription records
      await tx.userSubscription.deleteMany({
        where: {
          userId,
          paymentProcessorSubscriptionId: {
            in: testIdentifiers,
          },
        },
      });

      console.log(`Cleaned up test data for user ${userId}: ${testIdentifiers.join(', ')}`);
    });
  }
}

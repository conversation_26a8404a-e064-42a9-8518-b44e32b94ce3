import type { SubscriptionStatus } from '../plans';
import { PaymentPlanId } from '../plans';
import { PrismaClient } from '@prisma/client';
import { SubscriptionMappingService } from '../subscriptionMapping';

export const updateUserStripePaymentDetails = (
  { userStripeId, subscriptionPlan, subscriptionStatus, datePaid, numOfCreditsPurchased }: {
    userStripeId: string;
    subscriptionPlan?: PaymentPlanId;
    subscriptionStatus?: SubscriptionStatus;
    numOfCreditsPurchased?: number;
    datePaid?: Date;
  },
  userDelegate: PrismaClient['user']
) => {
  return userDelegate.update({
    where: {
      paymentProcessorUserId: userStripeId
    },
    data: {
      paymentProcessorUserId: userStripeId,
      subscriptionPlan,
      subscriptionStatus,
      datePaid,
      credits: numOfCreditsPurchased !== undefined ? { increment: numOfCreditsPurchased } : undefined,
    },
  });
};

/**
 * Enhanced version that can optionally create UserSubscription records
 * alongside User model updates using transactions
 */
export const updateUserStripePaymentDetailsWithSubscription = async (
  {
    userStripeId,
    subscriptionPlan,
    subscriptionStatus,
    datePaid,
    numOfCreditsPurchased,
    // New parameters for UserSubscription creation
    createUserSubscription = false,
    paymentProcessorSubscriptionId,
    subscriptionDuration = 30, // days
    userId,
  }: {
    userStripeId: string;
    subscriptionPlan?: PaymentPlanId;
    subscriptionStatus?: SubscriptionStatus;
    numOfCreditsPurchased?: number;
    datePaid?: Date;
    // New optional parameters
    createUserSubscription?: boolean;
    paymentProcessorSubscriptionId?: string;
    subscriptionDuration?: number;
    userId?: string;
  },
  prismaClient: PrismaClient
) => {
  if (!createUserSubscription || !subscriptionPlan) {
    // Fallback to legacy function if not creating UserSubscription
    return updateUserStripePaymentDetails(
      { userStripeId, subscriptionPlan, subscriptionStatus, datePaid, numOfCreditsPurchased },
      prismaClient.user
    );
  }

  // Use transaction for dual system operation
  return prismaClient.$transaction(async (tx) => {
    // Update User model (legacy compatibility)
    const updatedUser = await tx.user.update({
      where: { paymentProcessorUserId: userStripeId },
      data: {
        paymentProcessorUserId: userStripeId,
        subscriptionPlan,
        subscriptionStatus,
        datePaid,
        credits: numOfCreditsPurchased !== undefined ? { increment: numOfCreditsPurchased } : undefined,
      },
    });

    // Create or update UserSubscription record (new system)
    const subscriptionId = SubscriptionMappingService.getSubscriptionId(subscriptionPlan);
    if (subscriptionId && paymentProcessorSubscriptionId) {
      const startDate = datePaid || new Date();
      const endDate = subscriptionStatus === 'active' ? new Date(startDate.getTime() + subscriptionDuration * 24 * 60 * 60 * 1000) : null;

      // Check if UserSubscription already exists
      const existingUserSubscription = await tx.userSubscription.findFirst({
        where: {
          userId: userId || updatedUser.id,
          paymentProcessorSubscriptionId,
        },
      });

      if (existingUserSubscription) {
        // Update existing UserSubscription
        await tx.userSubscription.update({
          where: { id: existingUserSubscription.id },
          data: {
            status: subscriptionStatus === 'active' ? 'active' :
                   subscriptionStatus === 'cancel_at_period_end' ? 'cancelled' :
                   subscriptionStatus === 'past_due' ? 'past_due' : 'expired',
            endDate,
            ...(numOfCreditsPurchased && { creditsAllocated: { increment: numOfCreditsPurchased } }),
          },
        });
      } else {
        // Create new UserSubscription
        await tx.userSubscription.create({
          data: {
            userId: userId || updatedUser.id,
            subscriptionId,
            status: subscriptionStatus === 'active' ? 'active' :
                   subscriptionStatus === 'cancel_at_period_end' ? 'cancelled' :
                   subscriptionStatus === 'past_due' ? 'past_due' : 'pending',
            startDate,
            endDate,
            creditsAllocated: numOfCreditsPurchased || 0,
            paymentProcessorSubscriptionId,
          },
        });
      }
    }

    return updatedUser;
  });
};

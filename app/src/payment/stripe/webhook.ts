import { type MiddlewareConfigFn, HttpError } from 'wasp/server';
import { type PaymentsWebhook } from 'wasp/server/api';
import { type PrismaClient } from '@prisma/client';
import express from 'express';
import { Stripe } from 'stripe';
import { stripe } from './stripeClient';
import { paymentPlans, PaymentPlanId, SubscriptionStatus, PaymentPlanEffect, PaymentPlan } from '../plans';
import { updateUserStripePaymentDetails } from './paymentDetails';
import { emailSender } from 'wasp/server/email';
import { assertUnreachable } from '../../shared/utils';
import { requireNodeEnvVar } from '../../server/utils';
import { z } from 'zod';
import { SubscriptionMappingService } from '../subscriptionMapping';

export const stripeWebhook: PaymentsWebhook = async (request, response, context) => {
  const secret = requireNodeEnvVar('STRIPE_WEBHOOK_SECRET');
  const sig = request.headers['stripe-signature'];
  if (!sig) {
    throw new HttpError(400, 'Stripe Webhook Signature Not Provided');
  }
  let event: Stripe.Event;
  try {
    event = stripe.webhooks.constructEvent(request.body, sig, secret);
  } catch (err) {
    throw new HttpError(400, 'Error Constructing Stripe Webhook Event');
  }
  const prismaUserDelegate = context.entities.User;
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object as Stripe.Checkout.Session;
      await handleCheckoutSessionCompleted(session, context);
      break;
    case 'invoice.paid':
      const invoice = event.data.object as Stripe.Invoice;
      await handleInvoicePaid(invoice, context);
      break;
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object as Stripe.PaymentIntent;
      await handlePaymentIntentSucceeded(paymentIntent, context);
      break;
    case 'customer.subscription.updated':
      const updatedSubscription = event.data.object as Stripe.Subscription;
      await handleCustomerSubscriptionUpdated(updatedSubscription, context);
      break;
    case 'customer.subscription.deleted':
      const deletedSubscription = event.data.object as Stripe.Subscription;
      await handleCustomerSubscriptionDeleted(deletedSubscription, context);
      break;
    default:
      // If you'd like to handle more events, you can add more cases above.
      // When deploying your app, you configure your webhook in the Stripe dashboard to only send the events that you're
      // handling above and that are necessary for the functioning of your app. See: https://docs.opensaas.sh/guides/deploying/#setting-up-your-stripe-webhook
      // In development, it is likely that you will receive other events that you are not handling, and that's fine. These can be ignored without any issues.
      console.error('Unhandled event type: ', event.type);
  }
  response.json({ received: true }); // Stripe expects a 200 response to acknowledge receipt of the webhook
};

export const stripeMiddlewareConfigFn: MiddlewareConfigFn = (middlewareConfig) => {
  // We need to delete the default 'express.json' middleware and replace it with 'express.raw' middleware
  // because webhook data in the body of the request as raw JSON, not as JSON in the body of the request.
  middlewareConfig.delete('express.json');
  middlewareConfig.set('express.raw', express.raw({ type: 'application/json' }));
  return middlewareConfig;
};

// Because a checkout session completed could potentially result in a failed payment,
// we can update the user's payment details here, but confirm credits or a subscription
// if the payment succeeds in other, more specific, webhooks.
export async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session,
  context: any
) {
  const userStripeId = validateUserStripeIdOrThrow(session.customer);

  // Get metadata from session (includes subscription metadata from checkout)
  const { userId, subscriptionId, paymentPlanId } = session.metadata || {};

  const { line_items } = await stripe.checkout.sessions.retrieve(session.id, {
    expand: ['line_items'],
  });
  const lineItemPriceId = extractPriceId(line_items);
  const planId = getPlanIdByPriceId(lineItemPriceId);
  const plan = paymentPlans[planId];

  console.log(`Checkout session completed for user ${userStripeId}, session ${session.id}`);
  console.log(`Metadata: userId=${userId}, subscriptionId=${subscriptionId}, paymentPlanId=${paymentPlanId}`);

  if (plan.effect.kind === 'credits') {
    // For credit purchases, we'll handle UserSubscription creation in payment_intent.succeeded
    console.log(`Credit purchase detected, will handle in payment_intent.succeeded webhook`);
    return;
  }

  const { subscriptionPlan } = getPlanEffectPaymentDetails({ planId, planEffect: plan.effect });

  // Enhanced checkout session handling with metadata
  if (userId && subscriptionId && paymentPlanId) {
    try {
      await context.entities.$transaction(async (tx: any) => {
        // Update User model (legacy compatibility)
        await tx.user.update({
          where: { paymentProcessorUserId: userStripeId },
          data: {
            subscriptionPlan,
            // Don't update status here - wait for subscription webhook
          },
        });

        console.log(`User ${userStripeId} updated with subscription plan ${subscriptionPlan}`);
      });

      console.log(`Checkout session ${session.id} processed with enhanced metadata`);
      return;
    } catch (error) {
      console.error(`Failed to process checkout session ${session.id} with enhanced metadata:`, error);
    }
  }

  // Fallback to legacy system only
  return updateUserStripePaymentDetails({ userStripeId, subscriptionPlan }, context.entities.User);
}

// This is called when a subscription is purchased or renewed and payment succeeds.
// Invoices are not created for one-time payments, so we handle them in the payment_intent.succeeded webhook.
export async function handleInvoicePaid(invoice: Stripe.Invoice, context: any) {
  const userStripeId = validateUserStripeIdOrThrow(invoice.customer);
  const datePaid = new Date(invoice.period_start * 1000);

  // For invoice payments, we need to identify the subscription and update both systems
  if (invoice.subscription && typeof invoice.subscription === 'string') {
    try {
      // Get the subscription details to find the plan
      const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
      const priceId = extractPriceId(subscription.items);
      const planId = getPlanIdByPriceId(priceId);
      const plan = paymentPlans[planId];

      if (plan.effect.kind === 'subscription') {
        const subscriptionId = SubscriptionMappingService.getSubscriptionId(planId);

        if (subscriptionId) {
          await context.entities.$transaction(async (tx: any) => {
            // Update User model (legacy compatibility)
            await tx.user.update({
              where: { paymentProcessorUserId: userStripeId },
              data: {
                datePaid,
                credits: { increment: plan.effect.amount },
              },
            });

            // Update UserSubscription record (new system)
            const existingUserSubscription = await tx.userSubscription.findFirst({
              where: {
                paymentProcessorSubscriptionId: invoice.subscription as string,
              },
            });

            if (existingUserSubscription) {
              // Extend subscription period and add credits
              const newEndDate = new Date(existingUserSubscription.endDate || new Date());
              newEndDate.setDate(newEndDate.getDate() + 30); // Extend by 30 days

              await tx.userSubscription.update({
                where: { id: existingUserSubscription.id },
                data: {
                  endDate: newEndDate,
                  creditsAllocated: { increment: plan.effect.amount },
                },
              });
            }
          });

          console.log(`Invoice ${invoice.id} processed with dual system for user ${userStripeId}`);
          return;
        }
      }
    } catch (error) {
      console.error(`Failed to process invoice ${invoice.id} with dual system:`, error);
    }
  }

  // Fallback to legacy system only
  return updateUserStripePaymentDetails({ userStripeId, datePaid }, context.entities.User);
}

export async function handlePaymentIntentSucceeded(
  paymentIntent: Stripe.PaymentIntent,
  context: any
) {
  // We handle invoices in the invoice.paid webhook. Invoices exist for subscription payments,
  // but not for one-time payment/credits products which use the Stripe `payment` mode on checkout sessions.
  if (paymentIntent.invoice) {
    return;
  }

  const userStripeId = validateUserStripeIdOrThrow(paymentIntent.customer);
  const datePaid = new Date(paymentIntent.created * 1000);

  // We capture the metadata from the payment intent that includes subscription information
  const { metadata } = paymentIntent;
  const { priceId, userId, subscriptionId: metadataSubscriptionId, paymentPlanId, planType, planAmount } = metadata;

  if (!priceId) {
    throw new HttpError(400, 'No price id found in payment intent');
  }

  const planId = getPlanIdByPriceId(priceId);
  const plan = paymentPlans[planId];
  if (plan.effect.kind === 'subscription') {
    return;
  }

  const { numOfCreditsPurchased } = getPlanEffectPaymentDetails({ planId, planEffect: plan.effect });

  console.log(`Payment intent succeeded for user ${userStripeId}, payment ${paymentIntent.id}`);
  console.log(`Metadata: userId=${userId}, subscriptionId=${metadataSubscriptionId}, paymentPlanId=${paymentPlanId}, planType=${planType}, planAmount=${planAmount}`);

  // For one-time credit purchases, create UserSubscription record
  // Prioritize metadata subscriptionId, fallback to mapping service
  const subscriptionId = metadataSubscriptionId || SubscriptionMappingService.getSubscriptionId(planId);
  if (subscriptionId && numOfCreditsPurchased) {
    try {
      await context.entities.$transaction(async (tx: any) => {
        // Find user by payment processor ID or metadata userId
        let user;
        if (userId) {
          user = await tx.user.findUnique({
            where: { id: userId },
          });
        }

        if (!user) {
          user = await tx.user.findUnique({
            where: { paymentProcessorUserId: userStripeId },
          });
        }

        if (!user) {
          throw new Error(`User not found with Stripe ID: ${userStripeId} or userId: ${userId}`);
        }

        // Check for existing UserSubscription to prevent duplicates
        const existingUserSubscription = await tx.userSubscription.findFirst({
          where: {
            userId: user.id,
            paymentProcessorSubscriptionId: paymentIntent.id,
          },
        });

        if (existingUserSubscription) {
          console.log(`UserSubscription already exists for payment ${paymentIntent.id}, skipping creation`);
          return;
        }

        // Update User model (legacy compatibility)
        await tx.user.update({
          where: { id: user.id },
          data: {
            credits: { increment: numOfCreditsPurchased },
            datePaid,
          },
        });

        // Create UserSubscription record (new system)
        await tx.userSubscription.create({
          data: {
            userId: user.id,
            subscriptionId,
            status: 'active',
            startDate: datePaid,
            endDate: null, // One-time purchases don't expire
            creditsAllocated: numOfCreditsPurchased,
            paymentProcessorSubscriptionId: paymentIntent.id, // Use payment intent ID for one-time purchases
          },
        });

        console.log(`UserSubscription created for payment ${paymentIntent.id}, user ${user.id}, credits: ${numOfCreditsPurchased}`);
      });

      console.log(`Payment intent ${paymentIntent.id} processed with enhanced dual system for user ${userStripeId}`);
      return;
    } catch (error) {
      console.error(`Failed to process payment intent ${paymentIntent.id} with enhanced dual system:`, error);
    }
  }

  // Fallback to legacy system only
  return updateUserStripePaymentDetails(
    { userStripeId, numOfCreditsPurchased, datePaid },
    context.entities.User
  );
}

export async function handleCustomerSubscriptionUpdated(
  subscription: Stripe.Subscription,
  context: any
) {
  const userStripeId = validateUserStripeIdOrThrow(subscription.customer);
  let subscriptionStatus: SubscriptionStatus | undefined;

  const priceId = extractPriceId(subscription.items);
  const subscriptionPlan = getPlanIdByPriceId(priceId);

  // There are other subscription statuses, such as `trialing` that we are not handling and simply ignore
  // If you'd like to handle more statuses, you can add more cases above. Make sure to update the `SubscriptionStatus` type in `payment/plans.ts` as well
  if (subscription.status === SubscriptionStatus.Active) {
    subscriptionStatus = subscription.cancel_at_period_end
      ? SubscriptionStatus.CancelAtPeriodEnd
      : SubscriptionStatus.Active;
  } else if (subscription.status === SubscriptionStatus.PastDue) {
    subscriptionStatus = SubscriptionStatus.PastDue;
  }

  if (subscriptionStatus) {
    const subscriptionId = SubscriptionMappingService.getSubscriptionId(subscriptionPlan);

    console.log(`Subscription updated: ${subscription.id}, status: ${subscriptionStatus}, plan: ${subscriptionPlan}`);

    if (subscriptionId) {
      try {
        const user = await context.entities.$transaction(async (tx: any) => {
          // Update User model (legacy compatibility)
          const updatedUser = await tx.user.update({
            where: { paymentProcessorUserId: userStripeId },
            data: {
              subscriptionPlan,
              subscriptionStatus,
            },
          });

          // Update or create UserSubscription record (new system)
          const existingUserSubscription = await tx.userSubscription.findFirst({
            where: {
              paymentProcessorSubscriptionId: subscription.id,
            },
          });

          if (existingUserSubscription) {
            // Update existing subscription
            await tx.userSubscription.update({
              where: { id: existingUserSubscription.id },
              data: {
                status: subscription.cancel_at_period_end ? 'cancelled' : 'active',
              },
            });
          } else {
            // Create new subscription record if it doesn't exist
            const plan = paymentPlans[subscriptionPlan];
            if (plan.effect.kind === 'subscription') {
              const startDate = new Date(subscription.current_period_start * 1000);
              const endDate = new Date(subscription.current_period_end * 1000);

              await tx.userSubscription.create({
                data: {
                  userId: updatedUser.id,
                  subscriptionId,
                  status: subscription.cancel_at_period_end ? 'cancelled' : 'active',
                  startDate,
                  endDate,
                  creditsAllocated: plan.effect.amount,
                  paymentProcessorSubscriptionId: subscription.id,
                },
              });
            }
          }

          return updatedUser;
        });

        if (subscription.cancel_at_period_end) {
          if (user.email) {
            await emailSender.send({
              to: user.email,
              subject: 'We hate to see you go :(',
              text: 'We hate to see you go. Here is a sweet offer...',
              html: 'We hate to see you go. Here is a sweet offer...',
            });
          }
        }

        console.log(`Subscription ${subscription.id} updated with dual system for user ${userStripeId}`);
        return user;
      } catch (error) {
        console.error(`Failed to update subscription ${subscription.id} with dual system:`, error);
      }
    }

    // Fallback to legacy system only
    const user = await updateUserStripePaymentDetails(
      { userStripeId, subscriptionPlan, subscriptionStatus },
      context.entities.User
    );

    if (subscription.cancel_at_period_end) {
      if (user.email) {
        await emailSender.send({
          to: user.email,
          subject: 'We hate to see you go :(',
          text: 'We hate to see you go. Here is a sweet offer...',
          html: 'We hate to see you go. Here is a sweet offer...',
        });
      }
    }
    return user;
  }
}

export async function handleCustomerSubscriptionDeleted(
  subscription: Stripe.Subscription,
  context: any
) {
  const userStripeId = validateUserStripeIdOrThrow(subscription.customer);

  try {
    await context.entities.$transaction(async (tx: any) => {
      // Update User model (legacy compatibility)
      await tx.user.update({
        where: { paymentProcessorUserId: userStripeId },
        data: {
          subscriptionStatus: SubscriptionStatus.Deleted,
        },
      });

      // Update UserSubscription record (new system)
      const existingUserSubscription = await tx.userSubscription.findFirst({
        where: {
          paymentProcessorSubscriptionId: subscription.id,
        },
      });

      if (existingUserSubscription) {
        await tx.userSubscription.update({
          where: { id: existingUserSubscription.id },
          data: {
            status: 'expired',
          },
        });
      }
    });

    console.log(`Subscription ${subscription.id} deleted with dual system for user ${userStripeId}`);
  } catch (error) {
    console.error(`Failed to delete subscription ${subscription.id} with dual system:`, error);
    // Fallback to legacy system only
    return updateUserStripePaymentDetails(
      { userStripeId, subscriptionStatus: SubscriptionStatus.Deleted },
      context.entities.User
    );
  }
}

function validateUserStripeIdOrThrow(userStripeId: Stripe.Checkout.Session['customer']): string {
  if (!userStripeId) throw new HttpError(400, 'No customer id');
  if (typeof userStripeId !== 'string') throw new HttpError(400, 'Customer id is not a string');
  return userStripeId;
}

const LineItemsPriceSchema = z.object({
  data: z.array(
    z.object({
      price: z.object({
        id: z.string(),
      }),
    })
  ),
});

function extractPriceId(items: Stripe.Checkout.Session['line_items'] | Stripe.Subscription['items']) {
  const result = LineItemsPriceSchema.safeParse(items);
  if (!result.success) {
    throw new HttpError(400, 'No price id in stripe event object');
  }
  if (result.data.data.length > 1) {
    throw new HttpError(400, 'More than one item in stripe event object');
  }
  return result.data.data[0].price.id;
}

function getPlanIdByPriceId(priceId: string): PaymentPlanId {
  const planId = Object.values(PaymentPlanId).find(
    (planId) => paymentPlans[planId].getPaymentProcessorPlanId() === priceId
  );
  if (!planId) {
    throw new Error(`No plan with Stripe price id ${priceId}`);
  }
  return planId;
}

function getPlanEffectPaymentDetails({
  planId,
  planEffect,
}: {
  planId: PaymentPlanId;
  planEffect: PaymentPlanEffect;
}): {
  subscriptionPlan: PaymentPlanId | undefined;
  numOfCreditsPurchased: number | undefined;
} {
  switch (planEffect.kind) {
    case 'subscription':
      return { subscriptionPlan: planId, numOfCreditsPurchased: undefined };
    case 'credits':
      return { subscriptionPlan: undefined, numOfCreditsPurchased: planEffect.amount };
    default:
      assertUnreachable(planEffect);
  }
}

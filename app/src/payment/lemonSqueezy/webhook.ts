import { type MiddlewareConfigFn, HttpError } from 'wasp/server';
import { type PaymentsWebhook } from 'wasp/server/api';
import { type PrismaClient } from '@prisma/client';
import express from 'express';
import { paymentPlans, PaymentPlanId, SubscriptionStatus } from '../plans';
import { updateUserLemonSqueezyPaymentDetails } from './paymentDetails';
import { type Order, type Subscription, getCustomer } from '@lemonsqueezy/lemonsqueezy.js';
import crypto from 'crypto';
import { requireNodeEnvVar } from '../../server/utils';
import { SubscriptionMappingService } from '../subscriptionMapping';
import { CreditAllocationService } from '../creditAllocationService';

export const lemonSqueezyWebhook: PaymentsWebhook = async (request, response, context) => {
  try {
    const rawBody = request.body.toString('utf8');
    const signature = request.get('X-Signature');
    if (!signature) {
      throw new HttpError(400, 'Lemon Squeezy Webhook Signature Not Provided');
    }

    const secret = requireNodeEnvVar('LEMONSQUEEZY_WEBHOOK_SECRET');
    const hmac = crypto.createHmac('sha256', secret);
    const digest = Buffer.from(hmac.update(rawBody).digest('hex'), 'utf8');

    if (!crypto.timingSafeEqual(Buffer.from(signature, 'utf8'), digest)) {
      throw new HttpError(400, 'Invalid signature');
    }

    const event = JSON.parse(rawBody);
    const customData = event.meta.custom_data || {};
    const userId = customData.user_id;

    // Extract enhanced metadata from custom data
    const { paymentPlanId, subscriptionId, planType, planAmount } = customData;

    console.log(`LemonSqueezy webhook: ${event.meta.event_name}`);
    console.log(`Custom data: userId=${userId}, paymentPlanId=${paymentPlanId}, subscriptionId=${subscriptionId}, planType=${planType}, planAmount=${planAmount}`);

    const prismaUserDelegate = context.entities.User;
    switch (event.meta.event_name) {
      case 'order_created':
        await handleOrderCreated(event as Order, userId, customData, context);
        break;
      case 'subscription_created':
        await handleSubscriptionCreated(event as Subscription, userId, customData, context);
        break;
      case 'subscription_updated':
        await handleSubscriptionUpdated(event as Subscription, userId, customData, context);
        break;
      case 'subscription_cancelled':
        await handleSubscriptionCancelled(event as Subscription, userId, customData, context);
        break;
      case 'subscription_expired':
        await handleSubscriptionExpired(event as Subscription, userId, customData, context);
        break;
      default:
        console.error('Unhandled event type: ', event.meta.event_name);
    }

    response.status(200).json({ received: true });
  } catch (err) {
    console.error('Webhook error:', err);
    if (err instanceof HttpError) {
      response.status(err.statusCode).json({ error: err.message });
    } else {
      response.status(400).json({ error: 'Error Processing Lemon Squeezy Webhook Event' });
    }
  }
};

export const lemonSqueezyMiddlewareConfigFn: MiddlewareConfigFn = (middlewareConfig) => {
  // We need to delete the default 'express.json' middleware and replace it with 'express.raw' middleware
  // because webhook data in the body of the request as raw JSON, not as JSON in the body of the request.
  middlewareConfig.delete('express.json');
  middlewareConfig.set('express.raw', express.raw({ type: 'application/json' }));
  return middlewareConfig;
};

// This will fire for one-time payment orders AND subscriptions. But subscriptions will ALSO send a follow-up
// event of 'subscription_created'. So we use this handler mainly to process one-time, credit-based orders,
// as well as to save the customer portal URL and customer id for the user.
async function handleOrderCreated(data: Order, userId: string, customData: any, context: any) {
  console.log('Order created', data);
  const { customer_id, status, first_order_item, order_number } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();

  const planId = getPlanIdByVariantId(first_order_item.variant_id.toString());
  const plan = paymentPlans[planId];

  let numOfCreditsPurchased: number | undefined = undefined;
  let datePaid: Date | undefined = undefined;

  if (status === 'paid' && plan.effect.kind === 'credits') {
    numOfCreditsPurchased = plan.effect.amount;
    datePaid = new Date();

    // Use CreditAllocationService for dual system credit allocation with validation
    const allocationResult = await CreditAllocationService.allocateCredits(
      {
        userId,
        credits: numOfCreditsPurchased,
        planId,
        paymentProcessorSubscriptionId: order_number.toString(),
        source: 'payment',
        description: `LemonSqueezy order ${order_number}`,
      },
      context.entities
    );

    if (allocationResult.success) {
      // Update additional user details (payment processor ID, date paid)
      await updateUserLemonSqueezyPaymentDetails(
        { lemonSqueezyId, userId, datePaid },
        context.entities.User
      );
      console.log(`Order ${order_number} processed with dual system for user ${lemonSqueezyId}: ${allocationResult.message}`);
    } else {
      console.error(`Failed to allocate credits for order ${order_number}: ${allocationResult.message}`);
      // Fallback to legacy system only
      await updateUserLemonSqueezyPaymentDetails(
        { lemonSqueezyId, userId, numOfCreditsPurchased, datePaid },
        context.entities.User
      );
    }
  } else {
    // For non-paid or non-credit orders, just update user details
    await updateUserLemonSqueezyPaymentDetails(
      { lemonSqueezyId, userId, datePaid },
      context.entities.User
    );
  }

  console.log(`Order ${order_number} created for user ${lemonSqueezyId}`);
}

async function handleSubscriptionCreated(
  data: Subscription,
  userId: string,
  customData: any,
  context: any
) {
  console.log('Subscription created', data);
  const { customer_id, status, variant_id } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();
  const lemonSqueezySubscriptionId = data.data.id.toString();

  const planId = getPlanIdByVariantId(variant_id.toString());

  if (status === 'active') {
    const plan = paymentPlans[planId];
    const lemonSqueezyCustomerPortalUrl = await fetchUserCustomerPortalUrl({ lemonSqueezyId });

    let numOfCreditsPurchased: number | undefined = undefined;
    let numOfQueuesPurchased: number | 1 = 1;
    let datePaid: Date | undefined = undefined;

    if (status === 'active' && plan.effect.kind === 'subscription') {
      numOfCreditsPurchased = plan.effect.amount;
      numOfQueuesPurchased = plan.effect.queues || 1;
      datePaid = new Date();

      // Get subscription UUID for new system (prioritize metadata)
      const metadataSubscriptionId = customData.subscriptionId;
      const subscriptionId = metadataSubscriptionId || SubscriptionMappingService.getSubscriptionId(planId);

      console.log(`Creating subscription: ${lemonSqueezySubscriptionId}, plan: ${planId}, subscriptionId: ${subscriptionId}`);

      if (subscriptionId) {
        try {
          await context.entities.$transaction(async (tx: any) => {
            // Update User model (legacy compatibility)
            await tx.user.update({
              where: { id: userId },
              data: {
                paymentProcessorUserId: lemonSqueezyId,
                lemonSqueezyCustomerPortalUrl,
                subscriptionPlan: planId,
                subscriptionStatus: status as SubscriptionStatus,
                datePaid,
                credits: { increment: numOfCreditsPurchased },
                queues: numOfQueuesPurchased,
              },
            });

            // Create UserSubscription record (new system)
            const startDate = new Date();
            const endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 30); // Assuming monthly subscription

            await tx.userSubscription.create({
              data: {
                userId,
                subscriptionId,
                status: 'active',
                startDate,
                endDate,
                creditsAllocated: numOfCreditsPurchased,
                paymentProcessorSubscriptionId: lemonSqueezySubscriptionId,
              },
            });
          });

          console.log(`Subscription ${lemonSqueezySubscriptionId} processed with dual system for user ${lemonSqueezyId}`);
        } catch (error) {
          console.error(`Failed to process subscription ${lemonSqueezySubscriptionId} with dual system:`, error);
          // Fallback to legacy system only
          await updateUserLemonSqueezyPaymentDetails(
            {
              lemonSqueezyId,
              userId,
              subscriptionPlan: planId,
              numOfCreditsPurchased,
              numOfQueuesPurchased,
              lemonSqueezyCustomerPortalUrl,
              subscriptionStatus: status as SubscriptionStatus,
              datePaid,
            },
            context.entities.User
          );
        }
      } else {
        console.warn(`No subscription mapping found for plan ${planId}, using legacy system only`);
        await updateUserLemonSqueezyPaymentDetails(
          {
            lemonSqueezyId,
            userId,
            subscriptionPlan: planId,
            numOfCreditsPurchased,
            numOfQueuesPurchased,
            lemonSqueezyCustomerPortalUrl,
            subscriptionStatus: status as SubscriptionStatus,
            datePaid,
          },
          context.entities.User
        );
      }
    }
  } else {
    console.warn(`Unexpected status '${status}' for newly created subscription`);
  }

  console.log(`Subscription created for user ${lemonSqueezyId}`);
}

// NOTE: LemonSqueezy's 'subscription_updated' event is sent as a catch-all and fires even after 'subscription_created' & 'order_created'.
async function handleSubscriptionUpdated(
  data: Subscription,
  userId: string,
  customData: any,
  context: any
) {
  const { customer_id, status, variant_id } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();
  const lemonSqueezySubscriptionId = data.data.id.toString();

  const planId = getPlanIdByVariantId(variant_id.toString());

  // We ignore other statuses like 'paused' and 'unpaid' for now, because we block user usage if their status is NOT active.
  // Note that a status changes to 'past_due' on a failed payment retry, then after 4 unsuccesful payment retries status
  // becomes 'unpaid' and finally 'expired' (i.e. 'deleted').
  // NOTE: ability to pause or trial a subscription is something that has to be additionally configured in the lemon squeezy dashboard.
  // If you do enable these features, make sure to handle these statuses here.
  if (status === 'past_due' || status === 'active') {
    const subscriptionId = SubscriptionMappingService.getSubscriptionId(planId);

    if (subscriptionId) {
      try {
        await context.entities.$transaction(async (tx: any) => {
          // Update User model (legacy compatibility)
          await tx.user.update({
            where: { id: userId },
            data: {
              paymentProcessorUserId: lemonSqueezyId,
              subscriptionPlan: planId,
              subscriptionStatus: status as SubscriptionStatus,
              ...(status === 'active' && { datePaid: new Date() }),
            },
          });

          // Update UserSubscription record (new system)
          const existingUserSubscription = await tx.userSubscription.findFirst({
            where: {
              userId,
              paymentProcessorSubscriptionId: lemonSqueezySubscriptionId,
            },
          });

          if (existingUserSubscription) {
            await tx.userSubscription.update({
              where: { id: existingUserSubscription.id },
              data: {
                status: status === 'active' ? 'active' : 'past_due',
                ...(status === 'active' && {
                  endDate: new Date(new Date().setDate(new Date().getDate() + 30)) // Extend subscription
                }),
              },
            });
          }
        });

        console.log(`Subscription ${lemonSqueezySubscriptionId} updated with dual system for user ${lemonSqueezyId}`);
      } catch (error) {
        console.error(`Failed to update subscription ${lemonSqueezySubscriptionId} with dual system:`, error);
        // Fallback to legacy system only
        await updateUserLemonSqueezyPaymentDetails(
          {
            lemonSqueezyId,
            userId,
            subscriptionPlan: planId,
            subscriptionStatus: status as SubscriptionStatus,
            ...(status === 'active' && { datePaid: new Date() }),
          },
          context.entities.User
        );
      }
    } else {
      console.warn(`No subscription mapping found for plan ${planId}, using legacy system only`);
      await updateUserLemonSqueezyPaymentDetails(
        {
          lemonSqueezyId,
          userId,
          subscriptionPlan: planId,
          subscriptionStatus: status as SubscriptionStatus,
          ...(status === 'active' && { datePaid: new Date() }),
        },
        context.entities.User
      );
    }

    console.log(`Subscription updated for user ${lemonSqueezyId}`);
  }
}

async function handleSubscriptionCancelled(
  data: Subscription,
  userId: string,
  customData: any,
  context: any
) {
  const { customer_id } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();
  const lemonSqueezySubscriptionId = data.data.id.toString();

  try {
    await context.entities.$transaction(async (tx: any) => {
      // Update User model (legacy compatibility)
      await tx.user.update({
        where: { id: userId },
        data: {
          paymentProcessorUserId: lemonSqueezyId,
          // cancel_at_period_end is the Stripe equivalent of LemonSqueezy's cancelled
          subscriptionStatus: 'cancel_at_period_end' as SubscriptionStatus,
        },
      });

      // Update UserSubscription record (new system)
      const existingUserSubscription = await tx.userSubscription.findFirst({
        where: {
          userId,
          paymentProcessorSubscriptionId: lemonSqueezySubscriptionId,
        },
      });

      if (existingUserSubscription) {
        await tx.userSubscription.update({
          where: { id: existingUserSubscription.id },
          data: {
            status: 'cancelled',
          },
        });
      }
    });

    console.log(`Subscription ${lemonSqueezySubscriptionId} cancelled with dual system for user ${lemonSqueezyId}`);
  } catch (error) {
    console.error(`Failed to cancel subscription ${lemonSqueezySubscriptionId} with dual system:`, error);
    // Fallback to legacy system only
    await updateUserLemonSqueezyPaymentDetails(
      {
        lemonSqueezyId,
        userId,
        subscriptionStatus: 'cancel_at_period_end' as SubscriptionStatus,
      },
      context.entities.User
    );
  }

  console.log(`Subscription cancelled for user ${lemonSqueezyId}`);
}

async function handleSubscriptionExpired(
  data: Subscription,
  userId: string,
  customData: any,
  context: any
) {
  const { customer_id } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();
  const lemonSqueezySubscriptionId = data.data.id.toString();

  try {
    await context.entities.$transaction(async (tx: any) => {
      // Update User model (legacy compatibility)
      await tx.user.update({
        where: { id: userId },
        data: {
          paymentProcessorUserId: lemonSqueezyId,
          // deleted is the Stripe equivalent of LemonSqueezy's expired
          subscriptionStatus: SubscriptionStatus.Deleted,
        },
      });

      // Update UserSubscription record (new system)
      const existingUserSubscription = await tx.userSubscription.findFirst({
        where: {
          userId,
          paymentProcessorSubscriptionId: lemonSqueezySubscriptionId,
        },
      });

      if (existingUserSubscription) {
        await tx.userSubscription.update({
          where: { id: existingUserSubscription.id },
          data: {
            status: 'expired',
          },
        });
      }
    });

    console.log(`Subscription ${lemonSqueezySubscriptionId} expired with dual system for user ${lemonSqueezyId}`);
  } catch (error) {
    console.error(`Failed to expire subscription ${lemonSqueezySubscriptionId} with dual system:`, error);
    // Fallback to legacy system only
    await updateUserLemonSqueezyPaymentDetails(
      {
        lemonSqueezyId,
        userId,
        subscriptionStatus: SubscriptionStatus.Deleted,
      },
      context.entities.User
    );
  }

  console.log(`Subscription expired for user ${lemonSqueezyId}`);
}

async function fetchUserCustomerPortalUrl({ lemonSqueezyId }: { lemonSqueezyId: string }): Promise<string> {
  const { data: lemonSqueezyCustomer, error } = await getCustomer(lemonSqueezyId);
  if (error) {
    throw new Error(
      `Error fetching customer portal URL for user lemonsqueezy id ${lemonSqueezyId}: ${error}`
    );
  }
  const customerPortalUrl = lemonSqueezyCustomer.data.attributes.urls.customer_portal;
  if (!customerPortalUrl) {
    throw new Error(`No customer portal URL found for user lemonsqueezy id ${lemonSqueezyId}`);
  }
  return customerPortalUrl;
}

function getPlanIdByVariantId(variantId: string): PaymentPlanId {
  const planId = Object.values(PaymentPlanId).find(
    (planId) => paymentPlans[planId].getPaymentProcessorPlanId() === variantId
  );
  if (!planId) {
    throw new Error(`No plan with LemonSqueezy variant id ${variantId}`);
  }
  return planId;
}


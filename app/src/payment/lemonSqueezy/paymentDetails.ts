import type { SubscriptionStatus } from '../plans';
import { PaymentPlanId } from '../plans';
import { PrismaClient } from '@prisma/client';
import { SubscriptionMappingService } from '../subscriptionMapping';

export const updateUserLemonSqueezyPaymentDetails = async (
  { lemonSqueezyId, userId, subscriptionPlan, subscriptionStatus, datePaid, numOfCreditsPurchased, lemonSqueezyCustomerPortalUrl , numOfQueuesPurchased }: {
    lemonSqueezyId: string;
    userId: string;
    subscriptionPlan?: PaymentPlanId;
    subscriptionStatus?: SubscriptionStatus;
    numOfCreditsPurchased?: number;
    lemonSqueezyCustomerPortalUrl?: string;
    datePaid?: Date;
    numOfQueuesPurchased?: number;
  },
  prismaUserDelegate: PrismaClient['user']
) => {
  let queues: number  = 1;
  if(numOfQueuesPurchased !== undefined) {
    queues = numOfQueuesPurchased;
  }
  return prismaUserDelegate.update({
    where: {
      id: userId,
    },
    data: {
      paymentProcessorUserId: lemonSqueezyId,
      lemonSqueezyCustomerPortalUrl,
      subscriptionPlan,
      subscriptionStatus,
      datePaid,
      credits: numOfCreditsPurchased !== undefined ? { increment: numOfCreditsPurchased } : undefined,
      queues:queues,
    },
  });
};

/**
 * Enhanced version that can optionally create UserSubscription records
 * alongside User model updates using transactions
 */
export const updateUserLemonSqueezyPaymentDetailsWithSubscription = async (
  {
    lemonSqueezyId,
    userId,
    subscriptionPlan,
    subscriptionStatus,
    datePaid,
    numOfCreditsPurchased,
    lemonSqueezyCustomerPortalUrl,
    numOfQueuesPurchased,
    // New parameters for UserSubscription creation
    createUserSubscription = false,
    paymentProcessorSubscriptionId,
    subscriptionDuration = 30, // days
  }: {
    lemonSqueezyId: string;
    userId: string;
    subscriptionPlan?: PaymentPlanId;
    subscriptionStatus?: SubscriptionStatus;
    numOfCreditsPurchased?: number;
    lemonSqueezyCustomerPortalUrl?: string;
    datePaid?: Date;
    numOfQueuesPurchased?: number;
    // New optional parameters
    createUserSubscription?: boolean;
    paymentProcessorSubscriptionId?: string;
    subscriptionDuration?: number;
  },
  prismaClient: PrismaClient
) => {
  if (!createUserSubscription || !subscriptionPlan) {
    // Fallback to legacy function if not creating UserSubscription
    return updateUserLemonSqueezyPaymentDetails(
      { lemonSqueezyId, userId, subscriptionPlan, subscriptionStatus, datePaid, numOfCreditsPurchased, lemonSqueezyCustomerPortalUrl, numOfQueuesPurchased },
      prismaClient.user
    );
  }

  // Use transaction for dual system operation
  return prismaClient.$transaction(async (tx) => {
    let queues: number = 1;
    if (numOfQueuesPurchased !== undefined) {
      queues = numOfQueuesPurchased;
    }

    // Update User model (legacy compatibility)
    const updatedUser = await tx.user.update({
      where: { id: userId },
      data: {
        paymentProcessorUserId: lemonSqueezyId,
        lemonSqueezyCustomerPortalUrl,
        subscriptionPlan,
        subscriptionStatus,
        datePaid,
        credits: numOfCreditsPurchased !== undefined ? { increment: numOfCreditsPurchased } : undefined,
        queues,
      },
    });

    // Create or update UserSubscription record (new system)
    const subscriptionId = SubscriptionMappingService.getSubscriptionId(subscriptionPlan);
    if (subscriptionId && paymentProcessorSubscriptionId) {
      const startDate = datePaid || new Date();
      const endDate = subscriptionStatus === 'active' ? new Date(startDate.getTime() + subscriptionDuration * 24 * 60 * 60 * 1000) : null;

      // Check if UserSubscription already exists
      const existingUserSubscription = await tx.userSubscription.findFirst({
        where: {
          userId,
          paymentProcessorSubscriptionId,
        },
      });

      if (existingUserSubscription) {
        // Update existing UserSubscription
        await tx.userSubscription.update({
          where: { id: existingUserSubscription.id },
          data: {
            status: subscriptionStatus === 'active' ? 'active' :
                   subscriptionStatus === 'cancel_at_period_end' ? 'cancelled' :
                   subscriptionStatus === 'past_due' ? 'past_due' : 'expired',
            endDate,
            ...(numOfCreditsPurchased && { creditsAllocated: { increment: numOfCreditsPurchased } }),
          },
        });
      } else {
        // Create new UserSubscription
        await tx.userSubscription.create({
          data: {
            userId,
            subscriptionId,
            status: subscriptionStatus === 'active' ? 'active' :
                   subscriptionStatus === 'cancel_at_period_end' ? 'cancelled' :
                   subscriptionStatus === 'past_due' ? 'past_due' : 'pending',
            startDate,
            endDate,
            creditsAllocated: numOfCreditsPurchased || 0,
            paymentProcessorSubscriptionId,
          },
        });
      }
    }

    return updatedUser;
  });
};

import { type PrismaClient } from '@prisma/client';
import { PaymentPlanId } from './plans';

/**
 * Service for mapping between legacy PaymentPlanId enum and new Subscription UUIDs
 * Provides bidirectional mapping to maintain compatibility between systems
 */
export class SubscriptionMappingService {
  private static mappings: Map<PaymentPlanId, string> = new Map();
  private static reverseMappings: Map<string, PaymentPlanId> = new Map();
  private static initialized = false;

  /**
   * Initialize the mapping service by loading mappings from the database
   * This should be called once during application startup
   */
  static async initialize(prisma: PrismaClient): Promise<void> {
    try {
      console.log('Initializing subscription mappings...');
      
      // Define the expected mappings based on subscription plan names
      // These names should match what's in the subscription seed file
      const expectedMappings = [
        { planId: PaymentPlanId.Free, subscriptionName: 'Free Plan' },
        { planId: PaymentPlanId.Hobby, subscriptionName: 'Hobby Plan' },
        { planId: PaymentPlanId.Pro, subscriptionName: 'Pro Plan' },
        { planId: PaymentPlanId.Credits10, subscriptionName: 'Credits Pack - Small' },
      ];

      // Load mappings from database
      for (const mapping of expectedMappings) {
        const subscription = await prisma.subscription.findFirst({
          where: { 
            name: mapping.subscriptionName,
            isActive: true 
          }
        });

        if (subscription) {
          this.setMapping(mapping.planId, subscription.id);
          console.log(`  Mapped ${mapping.planId} -> ${subscription.id} (${mapping.subscriptionName})`);
        } else {
          console.warn(`  Warning: No subscription found for ${mapping.subscriptionName}`);
        }
      }

      this.initialized = true;
      console.log(`Subscription mappings initialized with ${this.mappings.size} mappings`);
    } catch (error) {
      console.error('Failed to initialize subscription mappings:', error);
      throw error;
    }
  }

  /**
   * Set a mapping between PaymentPlanId and Subscription UUID
   */
  static setMapping(planId: PaymentPlanId, subscriptionId: string): void {
    this.mappings.set(planId, subscriptionId);
    this.reverseMappings.set(subscriptionId, planId);
  }

  /**
   * Get Subscription UUID from PaymentPlanId
   */
  static getSubscriptionId(planId: PaymentPlanId): string | undefined {
    if (!this.initialized) {
      console.warn('SubscriptionMappingService not initialized. Call initialize() first.');
    }
    return this.mappings.get(planId);
  }

  /**
   * Get PaymentPlanId from Subscription UUID
   */
  static getPaymentPlanId(subscriptionId: string): PaymentPlanId | undefined {
    if (!this.initialized) {
      console.warn('SubscriptionMappingService not initialized. Call initialize() first.');
    }
    return this.reverseMappings.get(subscriptionId);
  }

  /**
   * Check if the service is initialized
   */
  static isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get all current mappings (for debugging)
   */
  static getAllMappings(): { planId: PaymentPlanId; subscriptionId: string }[] {
    const result: { planId: PaymentPlanId; subscriptionId: string }[] = [];
    this.mappings.forEach((subscriptionId, planId) => {
      result.push({ planId, subscriptionId });
    });
    return result;
  }

  /**
   * Validate that all PaymentPlanId values have corresponding mappings
   */
  static validateMappings(): { isValid: boolean; missingMappings: PaymentPlanId[] } {
    const allPlanIds = Object.values(PaymentPlanId);
    const missingMappings: PaymentPlanId[] = [];

    for (const planId of allPlanIds) {
      if (!this.mappings.has(planId)) {
        missingMappings.push(planId);
      }
    }

    return {
      isValid: missingMappings.length === 0,
      missingMappings
    };
  }

  /**
   * Create mappings for new subscription plans
   * This is useful when adding new plans to the system
   */
  static async createMapping(
    prisma: PrismaClient,
    planId: PaymentPlanId,
    subscriptionName: string
  ): Promise<string | null> {
    try {
      const subscription = await prisma.subscription.findFirst({
        where: { 
          name: subscriptionName,
          isActive: true 
        }
      });

      if (subscription) {
        this.setMapping(planId, subscription.id);
        console.log(`Created mapping: ${planId} -> ${subscription.id} (${subscriptionName})`);
        return subscription.id;
      } else {
        console.error(`No subscription found with name: ${subscriptionName}`);
        return null;
      }
    } catch (error) {
      console.error(`Failed to create mapping for ${planId}:`, error);
      return null;
    }
  }

  /**
   * Refresh mappings from database
   * Useful if subscription data has changed
   */
  static async refresh(prisma: PrismaClient): Promise<void> {
    this.mappings.clear();
    this.reverseMappings.clear();
    this.initialized = false;
    await this.initialize(prisma);
  }
}

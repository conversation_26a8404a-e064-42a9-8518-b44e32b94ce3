import { type PrismaClient } from '@prisma/client';
import { PaymentPlanId } from './plans';
import { SubscriptionMappingService } from './subscriptionMapping';

/**
 * Service for managing dual credit allocation between legacy User.credits 
 * and new UserSubscription.creditsAllocated fields with validation to prevent double allocation
 */
export class CreditAllocationService {
  
  /**
   * Allocate credits with validation to prevent double allocation
   * This is the main function that should be used for all credit allocations
   */
  static async allocateCredits(
    {
      userId,
      credits,
      planId,
      paymentProcessorSubscriptionId,
      source = 'payment',
      description,
    }: {
      userId: string;
      credits: number;
      planId?: PaymentPlanId;
      paymentProcessorSubscriptionId?: string;
      source?: 'payment' | 'subscription' | 'manual' | 'refund';
      description?: string;
    },
    prismaClient: PrismaClient
  ): Promise<{ success: boolean; message: string; userCredits?: number }> {
    
    if (credits <= 0) {
      return { success: false, message: 'Credits must be positive' };
    }

    try {
      return await prismaClient.$transaction(async (tx) => {
        // Check for existing allocation to prevent double allocation
        if (paymentProcessorSubscriptionId) {
          const existingAllocation = await tx.userSubscription.findFirst({
            where: {
              userId,
              paymentProcessorSubscriptionId,
              creditsAllocated: { gt: 0 },
            },
          });

          if (existingAllocation) {
            console.warn(`Credits already allocated for payment ${paymentProcessorSubscriptionId}, skipping allocation`);
            return { 
              success: false, 
              message: 'Credits already allocated for this payment',
              userCredits: (await tx.user.findUnique({ where: { id: userId }, select: { credits: true } }))?.credits 
            };
          }
        }

        // Allocate credits to User model (legacy system)
        const updatedUser = await tx.user.update({
          where: { id: userId },
          data: {
            credits: { increment: credits },
          },
          select: { credits: true },
        });

        // Allocate credits to UserSubscription if applicable (new system)
        if (planId && paymentProcessorSubscriptionId) {
          const subscriptionId = SubscriptionMappingService.getSubscriptionId(planId);
          
          if (subscriptionId) {
            // Check if UserSubscription exists
            const existingUserSubscription = await tx.userSubscription.findFirst({
              where: {
                userId,
                paymentProcessorSubscriptionId,
              },
            });

            if (existingUserSubscription) {
              // Update existing UserSubscription
              await tx.userSubscription.update({
                where: { id: existingUserSubscription.id },
                data: {
                  creditsAllocated: { increment: credits },
                },
              });
            } else {
              // Create new UserSubscription record for credit tracking
              await tx.userSubscription.create({
                data: {
                  userId,
                  subscriptionId,
                  status: 'active',
                  startDate: new Date(),
                  endDate: null, // One-time credit allocations don't expire
                  creditsAllocated: credits,
                  paymentProcessorSubscriptionId,
                },
              });
            }
          }
        }

        console.log(`Allocated ${credits} credits to user ${userId} from ${source}${description ? `: ${description}` : ''}`);
        
        return { 
          success: true, 
          message: `Successfully allocated ${credits} credits`,
          userCredits: updatedUser.credits 
        };
      });
    } catch (error) {
      console.error('Failed to allocate credits:', error);
      return { success: false, message: 'Failed to allocate credits' };
    }
  }

  /**
   * Refund credits with validation
   */
  static async refundCredits(
    {
      userId,
      credits,
      paymentProcessorSubscriptionId,
      reason = 'refund',
    }: {
      userId: string;
      credits: number;
      paymentProcessorSubscriptionId?: string;
      reason?: string;
    },
    prismaClient: PrismaClient
  ): Promise<{ success: boolean; message: string; userCredits?: number }> {
    
    if (credits <= 0) {
      return { success: false, message: 'Refund amount must be positive' };
    }

    try {
      return await prismaClient.$transaction(async (tx) => {
        // Get current user credits
        const user = await tx.user.findUnique({
          where: { id: userId },
          select: { credits: true },
        });

        if (!user) {
          return { success: false, message: 'User not found' };
        }

        // Ensure user has enough credits to refund
        if (user.credits < credits) {
          return { 
            success: false, 
            message: `Insufficient credits. User has ${user.credits}, trying to refund ${credits}` 
          };
        }

        // Deduct credits from User model
        const updatedUser = await tx.user.update({
          where: { id: userId },
          data: {
            credits: { decrement: credits },
          },
          select: { credits: true },
        });

        // Update UserSubscription if applicable
        if (paymentProcessorSubscriptionId) {
          const userSubscription = await tx.userSubscription.findFirst({
            where: {
              userId,
              paymentProcessorSubscriptionId,
            },
          });

          if (userSubscription && userSubscription.creditsAllocated >= credits) {
            await tx.userSubscription.update({
              where: { id: userSubscription.id },
              data: {
                creditsAllocated: { decrement: credits },
                status: userSubscription.creditsAllocated === credits ? 'cancelled' : userSubscription.status,
              },
            });
          }
        }

        console.log(`Refunded ${credits} credits from user ${userId} for ${reason}`);
        
        return { 
          success: true, 
          message: `Successfully refunded ${credits} credits`,
          userCredits: updatedUser.credits 
        };
      });
    } catch (error) {
      console.error('Failed to refund credits:', error);
      return { success: false, message: 'Failed to refund credits' };
    }
  }

  /**
   * Get credit allocation summary for a user
   */
  static async getCreditSummary(
    userId: string,
    prismaClient: PrismaClient
  ): Promise<{
    totalCredits: number;
    allocatedCredits: number;
    activeSubscriptions: number;
    creditHistory: Array<{
      source: string;
      credits: number;
      date: Date;
      status: string;
    }>;
  }> {
    try {
      const user = await prismaClient.user.findUnique({
        where: { id: userId },
        select: { credits: true },
      });

      const userSubscriptions = await prismaClient.userSubscription.findMany({
        where: { userId },
        include: { subscription: true },
        orderBy: { createdAt: 'desc' },
      });

      const totalCredits = user?.credits || 0;
      const allocatedCredits = userSubscriptions.reduce((sum, sub) => sum + sub.creditsAllocated, 0);
      const activeSubscriptions = userSubscriptions.filter(sub => sub.status === 'active').length;

      const creditHistory = userSubscriptions.map(sub => ({
        source: sub.subscription.name,
        credits: sub.creditsAllocated,
        date: sub.createdAt,
        status: sub.status,
      }));

      return {
        totalCredits,
        allocatedCredits,
        activeSubscriptions,
        creditHistory,
      };
    } catch (error) {
      console.error('Failed to get credit summary:', error);
      return {
        totalCredits: 0,
        allocatedCredits: 0,
        activeSubscriptions: 0,
        creditHistory: [],
      };
    }
  }

  /**
   * Validate credit allocation consistency between User and UserSubscription models
   */
  static async validateCreditConsistency(
    userId: string,
    prismaClient: PrismaClient
  ): Promise<{ isConsistent: boolean; details: string }> {
    try {
      const user = await prismaClient.user.findUnique({
        where: { id: userId },
        select: { credits: true },
      });

      const userSubscriptions = await prismaClient.userSubscription.findMany({
        where: { userId },
      });

      if (!user) {
        return { isConsistent: false, details: 'User not found' };
      }

      const totalAllocatedCredits = userSubscriptions.reduce((sum, sub) => sum + sub.creditsAllocated, 0);
      const userCredits = user.credits;

      // Note: User credits might be higher than allocated credits due to usage
      // This validation checks if there are any obvious inconsistencies
      if (totalAllocatedCredits > userCredits + 1000) { // Allow some buffer for usage
        return { 
          isConsistent: false, 
          details: `Allocated credits (${totalAllocatedCredits}) significantly exceed user credits (${userCredits})` 
        };
      }

      return { 
        isConsistent: true, 
        details: `User credits: ${userCredits}, Allocated credits: ${totalAllocatedCredits}` 
      };
    } catch (error) {
      console.error('Failed to validate credit consistency:', error);
      return { isConsistent: false, details: 'Validation failed' };
    }
  }
}

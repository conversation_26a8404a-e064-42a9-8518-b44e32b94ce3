import { type ServerSetupFn } from 'wasp/server';
import { SubscriptionMappingService } from '../payment/subscriptionMapping';
import { prisma } from 'wasp/server';

/**
 * Server setup function that runs on server startup.
 * This function is called before the server starts accepting requests.
 */
export const serverSetupFunction: ServerSetupFn = async ({ app, server }) => {
  console.log('[Server Setup] Starting server initialization...');

  try {
    // Initialize the SubscriptionMappingService
    console.log('[Server Setup] Initializing SubscriptionMappingService...');
    await SubscriptionMappingService.initialize(prisma);
    console.log('[Server Setup] SubscriptionMappingService initialized successfully');

    // Add any other server initialization logic here
    // For example:
    // - Setting up additional middleware
    // - Initializing external services
    // - Starting background processes

    console.log('[Server Setup] Server initialization completed successfully');
  } catch (error) {
    console.error('[Server Setup] Failed to initialize server:', error);
    
    // Log the error but don't crash the server
    // The SubscriptionMappingService will log warnings if not initialized
    console.error('[Server Setup] Server will continue but some features may not work correctly');
    
    // In production, you might want to exit the process:
    // process.exit(1);
  }
};

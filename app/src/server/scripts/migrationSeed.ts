import { type PrismaClient } from '@prisma/client';
import { migrateUserSubscriptions, type MigrationStats } from './migrateUserSubscriptions';

/**
 * Seed function wrapper for the user subscription migration
 * This allows running the migration through the wasp db seed command
 */
export async function seedMigrateUserSubscriptions(prismaClient: PrismaClient): Promise<MigrationStats> {
  console.log('🔄 Starting User Subscription Migration via Seed Command');
  console.log('=====================================================\n');

  try {
    // Run migration in dry-run mode by default for safety
    // To run in live mode, modify the dryRun parameter below
    const stats = await migrateUserSubscriptions(prismaClient, {
      dryRun: true, // Change to false for live migration
      batchSize: 100,
      skipExisting: true,
    });

    console.log('\n✅ Migration seed completed successfully!');
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️  ${stats.errors.length} errors occurred during migration.`);
      console.log('Please review the errors and consider running the migration again.');
    }

    return stats;
  } catch (error) {
    console.error('\n💥 Migration seed failed:', error);
    throw error;
  }
}

/**
 * Live migration seed function (use with caution)
 * This function runs the migration in live mode
 */
export async function seedMigrateUserSubscriptionsLive(prismaClient: PrismaClient): Promise<MigrationStats> {
  console.log('🚨 LIVE User Subscription Migration via Seed Command');
  console.log('===================================================\n');
  console.log('⚠️  WARNING: This will modify your database!');
  console.log('   Make sure you have a backup before proceeding.\n');

  try {
    const stats = await migrateUserSubscriptions(prismaClient, {
      dryRun: false, // Live migration
      batchSize: 100,
      skipExisting: true,
    });

    console.log('\n✅ Live migration seed completed successfully!');
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️  ${stats.errors.length} errors occurred during migration.`);
      console.log('Please review the errors and consider running the migration again.');
    }

    return stats;
  } catch (error) {
    console.error('\n💥 Live migration seed failed:', error);
    throw error;
  }
}

import { PrismaClient } from '@prisma/client';
import { SubscriptionMappingService } from '../../payment/subscriptionMapping';
import { PaymentPlanId } from '../../payment/plans';

/**
 * Migration script to convert existing User subscription data to UserSubscription records
 * This script handles the migration from legacy subscription fields to the new subscription system
 */

export interface MigrationStats {
  totalUsers: number;
  usersWithSubscriptions: number;
  migratedSubscriptions: number;
  skippedUsers: number;
  errors: string[];
}

interface LegacyUserData {
  id: string;
  subscriptionPlan: string | null;
  subscriptionStatus: string | null;
  datePaid: Date | null;
  credits: number;
  paymentProcessorUserId: string | null;
  lemonSqueezyCustomerPortalUrl: string | null;
  createdAt: Date;
}

export async function migrateUserSubscriptions(
  prisma: PrismaClient,
  options: {
    dryRun?: boolean;
    batchSize?: number;
    skipExisting?: boolean;
  } = {}
): Promise<MigrationStats> {
  const { dryRun = false, batchSize = 100, skipExisting = true } = options;
  
  console.log(`🚀 Starting user subscription migration (${dryRun ? 'DRY RUN' : 'LIVE RUN'})`);
  
  const stats: MigrationStats = {
    totalUsers: 0,
    usersWithSubscriptions: 0,
    migratedSubscriptions: 0,
    skippedUsers: 0,
    errors: [],
  };

  try {
    // Initialize SubscriptionMappingService if not already initialized
    if (!SubscriptionMappingService.isInitialized()) {
      console.log('📋 Initializing SubscriptionMappingService...');
      await SubscriptionMappingService.initialize(prisma);
    }

    // Get total user count
    stats.totalUsers = await prisma.user.count();
    console.log(`📊 Found ${stats.totalUsers} total users`);

    // Process users in batches
    let offset = 0;
    let processedUsers = 0;

    while (processedUsers < stats.totalUsers) {
      console.log(`📦 Processing batch ${Math.floor(processedUsers / batchSize) + 1} (users ${processedUsers + 1}-${Math.min(processedUsers + batchSize, stats.totalUsers)})`);

      const users = await prisma.user.findMany({
        select: {
          id: true,
          subscriptionPlan: true,
          subscriptionStatus: true,
          datePaid: true,
          credits: true,
          paymentProcessorUserId: true,
          lemonSqueezyCustomerPortalUrl: true,
          createdAt: true,
        },
        skip: offset,
        take: batchSize,
      });

      for (const user of users) {
        try {
          const migrationResult = await migrateUserSubscription(prisma, user, { dryRun, skipExisting });
          
          if (migrationResult.migrated) {
            stats.migratedSubscriptions++;
          } else if (migrationResult.skipped) {
            stats.skippedUsers++;
          }

          if (migrationResult.hasSubscription) {
            stats.usersWithSubscriptions++;
          }

        } catch (error) {
          const errorMsg = `Failed to migrate user ${user.id}: ${error}`;
          console.error(`❌ ${errorMsg}`);
          stats.errors.push(errorMsg);
        }
      }

      processedUsers += users.length;
      offset += batchSize;

      // Progress update
      const progress = ((processedUsers / stats.totalUsers) * 100).toFixed(1);
      console.log(`📈 Progress: ${progress}% (${processedUsers}/${stats.totalUsers} users processed)`);
    }

    // Final statistics
    console.log('\n📊 Migration Statistics:');
    console.log(`   Total Users: ${stats.totalUsers}`);
    console.log(`   Users with Subscriptions: ${stats.usersWithSubscriptions}`);
    console.log(`   Migrated Subscriptions: ${stats.migratedSubscriptions}`);
    console.log(`   Skipped Users: ${stats.skippedUsers}`);
    console.log(`   Errors: ${stats.errors.length}`);

    if (stats.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      stats.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    if (dryRun) {
      console.log('\n🔍 This was a DRY RUN - no data was actually migrated');
    } else {
      console.log('\n✅ Migration completed successfully!');
    }

  } catch (error) {
    console.error('💥 Migration failed:', error);
    stats.errors.push(`Migration failed: ${error}`);
  }

  return stats;
}

async function migrateUserSubscription(
  prisma: PrismaClient,
  user: LegacyUserData,
  options: { dryRun: boolean; skipExisting: boolean }
): Promise<{ migrated: boolean; skipped: boolean; hasSubscription: boolean }> {
  const { dryRun, skipExisting } = options;

  // Check if user has subscription data
  const hasSubscription = !!(user.subscriptionPlan && user.subscriptionStatus);
  
  if (!hasSubscription) {
    return { migrated: false, skipped: false, hasSubscription: false };
  }

  // Check if UserSubscription already exists for this user
  if (skipExisting) {
    const existingUserSubscription = await prisma.userSubscription.findFirst({
      where: { userId: user.id },
    });

    if (existingUserSubscription) {
      console.log(`⏭️  Skipping user ${user.id} - UserSubscription already exists`);
      return { migrated: false, skipped: true, hasSubscription: true };
    }
  }

  // Map legacy subscription plan to new subscription
  const paymentPlanId = mapLegacyPlanToPaymentPlanId(user.subscriptionPlan!);
  if (!paymentPlanId) {
    console.warn(`⚠️  Unknown subscription plan for user ${user.id}: ${user.subscriptionPlan}`);
    return { migrated: false, skipped: true, hasSubscription: true };
  }

  const subscriptionId = SubscriptionMappingService.getSubscriptionId(paymentPlanId);
  if (!subscriptionId) {
    console.warn(`⚠️  No subscription mapping found for plan ${paymentPlanId} (user ${user.id})`);
    return { migrated: false, skipped: true, hasSubscription: true };
  }

  // Calculate subscription dates
  const startDate = user.datePaid || user.createdAt;
  const endDate = calculateEndDate(startDate, paymentPlanId, user.subscriptionStatus!);

  // Estimate credits allocated (this is an approximation since we don't have exact history)
  const creditsAllocated = estimateCreditsAllocated(paymentPlanId, user.subscriptionStatus!);

  // Map subscription status
  const status = mapLegacyStatusToNewStatus(user.subscriptionStatus!);

  if (dryRun) {
    console.log(`🔍 [DRY RUN] Would migrate user ${user.id}:`);
    console.log(`   Plan: ${user.subscriptionPlan} → ${subscriptionId}`);
    console.log(`   Status: ${user.subscriptionStatus} → ${status}`);
    console.log(`   Start Date: ${startDate.toISOString()}`);
    console.log(`   End Date: ${endDate?.toISOString() || 'null'}`);
    console.log(`   Credits Allocated: ${creditsAllocated}`);
    return { migrated: true, skipped: false, hasSubscription: true };
  }

  // Create UserSubscription record
  await prisma.userSubscription.create({
    data: {
      userId: user.id,
      subscriptionId,
      status,
      startDate,
      endDate,
      creditsAllocated,
      paymentProcessorSubscriptionId: user.paymentProcessorUserId,
    },
  });

  console.log(`✅ Migrated user ${user.id}: ${user.subscriptionPlan} → ${subscriptionId}`);
  return { migrated: true, skipped: false, hasSubscription: true };
}

function mapLegacyPlanToPaymentPlanId(legacyPlan: string): PaymentPlanId | null {
  const planMapping: Record<string, PaymentPlanId> = {
    'free': PaymentPlanId.Free,
    'hobby': PaymentPlanId.Hobby,
    'pro': PaymentPlanId.Pro,
    'credits10': PaymentPlanId.Credits10,
    // Add any other legacy plan names that might exist
    'starter': PaymentPlanId.Hobby, // Alternative name
    'business': PaymentPlanId.Pro,  // Alternative name
  };

  return planMapping[legacyPlan.toLowerCase()] || null;
}

function mapLegacyStatusToNewStatus(legacyStatus: string): string {
  const statusMapping: Record<string, string> = {
    'active': 'active',
    'cancel_at_period_end': 'cancelled',
    'past_due': 'past_due',
    'deleted': 'expired',
    'cancelled': 'cancelled',
    'expired': 'expired',
  };

  return statusMapping[legacyStatus] || 'pending';
}

function calculateEndDate(startDate: Date, planId: PaymentPlanId, status: string): Date | null {
  // For expired or cancelled subscriptions, don't set an end date in the future
  if (status === 'deleted' || status === 'cancelled') {
    return new Date(); // Set to current date
  }

  // For one-time credit purchases, no end date
  if (planId === PaymentPlanId.Credits10) {
    return null;
  }

  // For active subscriptions, estimate end date based on plan type
  const endDate = new Date(startDate);
  
  // Most plans are monthly, so add 30 days
  endDate.setDate(endDate.getDate() + 30);
  
  return endDate;
}

function estimateCreditsAllocated(planId: PaymentPlanId, status: string): number {
  // This is an estimation since we don't have exact credit allocation history
  const planCredits: Record<PaymentPlanId, number> = {
    [PaymentPlanId.Free]: 3,
    [PaymentPlanId.Hobby]: 200,
    [PaymentPlanId.Pro]: 1000,
    [PaymentPlanId.Credits10]: 100,
  };

  return planCredits[planId] || 0;
}

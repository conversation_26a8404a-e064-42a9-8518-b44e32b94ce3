#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';
import { migrateUserSubscriptions } from './migrateUserSubscriptions';

/**
 * CLI script to run the user subscription migration
 * 
 * Usage:
 *   npm run migrate:subscriptions -- --dry-run
 *   npm run migrate:subscriptions -- --live
 *   npm run migrate:subscriptions -- --live --batch-size=50
 *   npm run migrate:subscriptions -- --live --no-skip-existing
 */

interface CliOptions {
  dryRun: boolean;
  batchSize: number;
  skipExisting: boolean;
  help: boolean;
}

function parseArgs(): CliOptions {
  const args = process.argv.slice(2);
  
  const options: CliOptions = {
    dryRun: true, // Default to dry run for safety
    batchSize: 100,
    skipExisting: true,
    help: false,
  };

  for (const arg of args) {
    if (arg === '--help' || arg === '-h') {
      options.help = true;
    } else if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--live') {
      options.dryRun = false;
    } else if (arg.startsWith('--batch-size=')) {
      const size = parseInt(arg.split('=')[1]);
      if (size > 0) {
        options.batchSize = size;
      }
    } else if (arg === '--no-skip-existing') {
      options.skipExisting = false;
    } else if (arg === '--skip-existing') {
      options.skipExisting = true;
    }
  }

  return options;
}

function printHelp() {
  console.log(`
🔄 User Subscription Migration Tool

This tool migrates existing User subscription data to the new UserSubscription model.

Usage:
  npm run migrate:subscriptions [options]

Options:
  --dry-run              Run in dry-run mode (default, no data changes)
  --live                 Run in live mode (actually migrate data)
  --batch-size=N         Process N users at a time (default: 100)
  --skip-existing        Skip users who already have UserSubscription records (default)
  --no-skip-existing     Process all users, even if they have UserSubscription records
  --help, -h             Show this help message

Examples:
  npm run migrate:subscriptions                           # Dry run with default settings
  npm run migrate:subscriptions -- --dry-run              # Explicit dry run
  npm run migrate:subscriptions -- --live                 # Live migration
  npm run migrate:subscriptions -- --live --batch-size=50 # Live migration with smaller batches
  npm run migrate:subscriptions -- --live --no-skip-existing # Migrate all users

Safety Notes:
  - Always run with --dry-run first to see what will be migrated
  - The script preserves all existing User model data
  - UserSubscription records are created alongside existing data
  - Use --skip-existing to avoid duplicate migrations
`);
}

async function main() {
  const options = parseArgs();

  if (options.help) {
    printHelp();
    process.exit(0);
  }

  console.log('🔄 User Subscription Migration Tool');
  console.log('=====================================\n');

  // Confirm live run
  if (!options.dryRun) {
    console.log('⚠️  WARNING: This is a LIVE RUN that will modify your database!');
    console.log('   Make sure you have a database backup before proceeding.\n');
    
    // In a real CLI, you might want to add a confirmation prompt here
    // For now, we'll just log the warning
  }

  console.log('Migration Options:');
  console.log(`  Mode: ${options.dryRun ? 'DRY RUN' : 'LIVE RUN'}`);
  console.log(`  Batch Size: ${options.batchSize}`);
  console.log(`  Skip Existing: ${options.skipExisting}`);
  console.log('');

  const prisma = new PrismaClient();

  try {
    const stats = await migrateUserSubscriptions(prisma, {
      dryRun: options.dryRun,
      batchSize: options.batchSize,
      skipExisting: options.skipExisting,
    });

    console.log('\n🎉 Migration completed!');
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️  ${stats.errors.length} errors occurred during migration.`);
      console.log('Please review the errors above and consider running the migration again.');
      process.exit(1);
    } else {
      console.log('\n✅ No errors occurred during migration.');
      process.exit(0);
    }

  } catch (error) {
    console.error('\n💥 Migration failed with error:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the migration
if (require.main === module) {
  main();
}

import { type PrismaClient } from '@prisma/client';

/**
 * Seeds the database with initial subscription plans
 */
export async function seedSubscriptionPlans(prismaClient: PrismaClient) {
  console.log('Seeding subscription plans...');

  const subscriptionPlans = [
    {
      name: 'Free Plan',
      description: 'Free plan for everyone to get started. Basic features with limited usage.',
      price: 0, // Free
      duration: 30, // 30 days
      interval: 'monthly',
      creditsIncluded: 3,
      features: JSON.stringify([
        '1 queue',
        'Basic support',
        'Essential features',
        'Email notifications',
        '3 credits per month'
      ]),
      isActive: true
    },
    {
      name: 'Hobby Plan',
      description: 'Perfect for small businesses and individual providers. Get started with essential features and moderate usage limits.',
      price: 200, // $2.00 (stored in cents)
      duration: 30, // 30 days
      interval: 'monthly',
      creditsIncluded: 50,
      features: JSON.stringify([
        '3 queues',
        'Basic support',
        'Standard features',
        'Email notifications',
        'Basic analytics',
        '50 credits per month'
      ]),
      isActive: true
    },
    {
      name: 'Pro Plan',
      description: 'For growing businesses that need advanced features and higher usage limits. Includes priority support and analytics.',
      price: 1000, // $10.00 (stored in cents)
      duration: 30, // 30 days
      interval: 'monthly',
      creditsIncluded: 200,
      features: JSON.stringify([
        '10 queues',
        'Priority support',
        'Advanced features',
        'SMS & Email notifications',
        'Advanced analytics',
        'Custom branding',
        'API access',
        '200 credits per month'
      ]),
      isActive: true
    },
    {
      name: 'Enterprise Plan',
      description: 'For large organizations with custom needs. Unlimited usage with dedicated support and custom integrations.',
      price: 5000, // $50.00 (stored in cents)
      duration: 30, // 30 days
      interval: 'monthly',
      creditsIncluded: 1000,
      features: JSON.stringify([
        'Unlimited queues',
        'Dedicated support',
        'All features included',
        'Custom integrations',
        'White-label solution',
        'Advanced security',
        'Custom reporting',
        '1000 credits per month'
      ]),
      isActive: true
    },
    {
      name: 'Credits Pack - Small',
      description: 'One-time credit purchase for occasional users. No subscription required.',
      price: 100, // $1.00 (stored in cents)
      duration: 365, // Credits don\'t expire for a year
      interval: 'one-time',
      creditsIncluded: 10,
      features: JSON.stringify([
        '10 credits',
        'No subscription required',
        'Credits valid for 1 year',
        'Pay as you go'
      ]),
      isActive: true
    },
    {
      name: 'Credits Pack - Medium',
      description: 'One-time credit purchase for regular users. Better value per credit.',
      price: 500, // $5.00 (stored in cents)
      duration: 365, // Credits don\'t expire for a year
      interval: 'one-time',
      creditsIncluded: 60,
      features: JSON.stringify([
        '60 credits',
        'No subscription required',
        'Credits valid for 1 year',
        'Better value per credit',
        'Pay as you go'
      ]),
      isActive: true
    },
    {
      name: 'Credits Pack - Large',
      description: 'One-time credit purchase for heavy users. Best value per credit.',
      price: 1000, // $10.00 (stored in cents)
      duration: 365, // Credits don\'t expire for a year
      interval: 'one-time',
      creditsIncluded: 150,
      features: JSON.stringify([
        '150 credits',
        'No subscription required',
        'Credits valid for 1 year',
        'Best value per credit',
        'Pay as you go'
      ]),
      isActive: true
    }
  ];

  for (const planData of subscriptionPlans) {
    try {
      const existingPlan = await prismaClient.subscription.findFirst({
        where: { name: planData.name }
      });

      if (existingPlan) {
        console.log(`  Subscription plan "${planData.name}" already exists. Updating...`);
        await prismaClient.subscription.update({
          where: { id: existingPlan.id },
          data: planData
        });
        console.log(`  Updated subscription plan: ${planData.name}`);
      } else {
        const createdPlan = await prismaClient.subscription.create({
          data: planData
        });
        console.log(`  Created subscription plan: ${createdPlan.name} (ID: ${createdPlan.id})`);
      }
    } catch (error) {
      console.error(`  Error creating/updating subscription plan "${planData.name}":`, error);
    }
  }

  console.log('Subscription plans seeding completed.');
}

import { PrismaClient } from '@prisma/client';
import { CreditValidationUtils } from '../../payment/creditValidationUtils';

/**
 * Validation script to check the results of the user subscription migration
 * This script helps ensure the migration was successful and data is consistent
 */

interface ValidationReport {
  totalUsers: number;
  usersWithLegacySubscriptions: number;
  usersWithNewSubscriptions: number;
  usersWithBothSystems: number;
  usersWithMissingMigration: number;
  creditConsistencyIssues: number;
  duplicateAllocations: number;
  validationErrors: string[];
  recommendations: string[];
}

export async function validateMigrationResults(
  prisma: PrismaClient,
  options: {
    sampleSize?: number;
    checkCreditConsistency?: boolean;
    checkDuplicates?: boolean;
  } = {}
): Promise<ValidationReport> {
  const { sampleSize = 1000, checkCreditConsistency = true, checkDuplicates = true } = options;
  
  console.log('🔍 Validating User Subscription Migration Results');
  console.log('================================================\n');

  const report: ValidationReport = {
    totalUsers: 0,
    usersWithLegacySubscriptions: 0,
    usersWithNewSubscriptions: 0,
    usersWithBothSystems: 0,
    usersWithMissingMigration: 0,
    creditConsistencyIssues: 0,
    duplicateAllocations: 0,
    validationErrors: [],
    recommendations: [],
  };

  try {
    // Get total user count
    report.totalUsers = await prisma.user.count();
    console.log(`📊 Total users in system: ${report.totalUsers}`);

    // Count users with legacy subscription data
    report.usersWithLegacySubscriptions = await prisma.user.count({
      where: {
        AND: [
          { subscriptionPlan: { not: null } },
          { subscriptionStatus: { not: null } },
        ],
      },
    });
    console.log(`📋 Users with legacy subscriptions: ${report.usersWithLegacySubscriptions}`);

    // Count users with new subscription data
    report.usersWithNewSubscriptions = await prisma.user.count({
      where: {
        subscriptions: {
          some: {},
        },
      },
    });
    console.log(`🆕 Users with new subscriptions: ${report.usersWithNewSubscriptions}`);

    // Find users with both systems
    const usersWithBoth = await prisma.user.findMany({
      where: {
        AND: [
          { subscriptionPlan: { not: null } },
          { subscriptionStatus: { not: null } },
          {
            subscriptions: {
              some: {},
            },
          },
        ],
      },
      select: { id: true },
    });
    report.usersWithBothSystems = usersWithBoth.length;
    console.log(`🔗 Users with both systems: ${report.usersWithBothSystems}`);

    // Find users with legacy subscriptions but no new subscriptions (missing migration)
    const usersWithMissingMigration = await prisma.user.findMany({
      where: {
        AND: [
          { subscriptionPlan: { not: null } },
          { subscriptionStatus: { not: null } },
          {
            subscriptions: {
              none: {},
            },
          },
        ],
      },
      select: { 
        id: true, 
        subscriptionPlan: true, 
        subscriptionStatus: true,
        datePaid: true,
      },
    });
    report.usersWithMissingMigration = usersWithMissingMigration.length;
    console.log(`❌ Users with missing migration: ${report.usersWithMissingMigration}`);

    if (report.usersWithMissingMigration > 0) {
      report.validationErrors.push(`${report.usersWithMissingMigration} users have legacy subscription data but no UserSubscription records`);
      report.recommendations.push('Run the migration script to migrate remaining users');
      
      // Log first few missing users for debugging
      const sampleMissing = usersWithMissingMigration.slice(0, 5);
      console.log('\n📋 Sample users with missing migration:');
      sampleMissing.forEach(user => {
        console.log(`   User ${user.id}: ${user.subscriptionPlan} (${user.subscriptionStatus})`);
      });
    }

    // Check for credit consistency issues
    if (checkCreditConsistency) {
      console.log('\n🔍 Checking credit consistency...');
      const creditValidation = await CreditValidationUtils.validateAllUserCredits(prisma, sampleSize);
      report.creditConsistencyIssues = creditValidation.inconsistentUsers.length;
      
      if (report.creditConsistencyIssues > 0) {
        report.validationErrors.push(`${report.creditConsistencyIssues} users have credit consistency issues`);
        report.recommendations.push('Review credit allocation logic and consider running credit reconciliation');
        
        console.log(`   Found ${report.creditConsistencyIssues} users with credit inconsistencies`);
        
        // Log first few inconsistent users
        const sampleInconsistent = creditValidation.inconsistentUsers.slice(0, 3);
        sampleInconsistent.forEach(user => {
          console.log(`   User ${user.userId}: ${user.userCredits} credits vs ${user.allocatedCredits} allocated (diff: ${user.difference})`);
        });
      } else {
        console.log('   ✅ No credit consistency issues found');
      }
    }

    // Check for duplicate allocations
    if (checkDuplicates) {
      console.log('\n🔍 Checking for duplicate allocations...');
      const duplicates = await CreditValidationUtils.findDuplicateAllocations(prisma);
      report.duplicateAllocations = duplicates.length;
      
      if (report.duplicateAllocations > 0) {
        report.validationErrors.push(`${report.duplicateAllocations} duplicate credit allocations found`);
        report.recommendations.push('Run duplicate allocation cleanup script');
        
        console.log(`   Found ${report.duplicateAllocations} duplicate allocations`);
        
        // Log first few duplicates
        const sampleDuplicates = duplicates.slice(0, 3);
        sampleDuplicates.forEach(dup => {
          console.log(`   Payment ${dup.paymentProcessorSubscriptionId}: ${dup.count} allocations, ${dup.totalCredits} total credits`);
        });
      } else {
        console.log('   ✅ No duplicate allocations found');
      }
    }

    // Generate recommendations based on findings
    if (report.usersWithLegacySubscriptions > 0 && report.usersWithNewSubscriptions === 0) {
      report.recommendations.push('No users have been migrated yet. Run the migration script.');
    } else if (report.usersWithMissingMigration === 0 && report.usersWithLegacySubscriptions > 0) {
      report.recommendations.push('All users with legacy subscriptions have been migrated successfully.');
    }

    if (report.usersWithBothSystems === report.usersWithLegacySubscriptions) {
      report.recommendations.push('Migration appears complete. Consider removing legacy subscription fields after thorough testing.');
    }

    // Print summary
    console.log('\n📊 Validation Summary:');
    console.log(`   Total Users: ${report.totalUsers}`);
    console.log(`   Legacy Subscriptions: ${report.usersWithLegacySubscriptions}`);
    console.log(`   New Subscriptions: ${report.usersWithNewSubscriptions}`);
    console.log(`   Both Systems: ${report.usersWithBothSystems}`);
    console.log(`   Missing Migration: ${report.usersWithMissingMigration}`);
    
    if (checkCreditConsistency) {
      console.log(`   Credit Issues: ${report.creditConsistencyIssues}`);
    }
    
    if (checkDuplicates) {
      console.log(`   Duplicate Allocations: ${report.duplicateAllocations}`);
    }

    // Print validation status
    if (report.validationErrors.length === 0) {
      console.log('\n✅ Validation passed! No issues found.');
    } else {
      console.log(`\n⚠️  Validation found ${report.validationErrors.length} issues:`);
      report.validationErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    // Print recommendations
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }

  } catch (error) {
    console.error('💥 Validation failed:', error);
    report.validationErrors.push(`Validation failed: ${error}`);
  }

  return report;
}

/**
 * CLI function to run validation
 */
export async function runValidation() {
  const prisma = new PrismaClient();

  try {
    const report = await validateMigrationResults(prisma, {
      sampleSize: 1000,
      checkCreditConsistency: true,
      checkDuplicates: true,
    });

    if (report.validationErrors.length > 0) {
      process.exit(1);
    } else {
      process.exit(0);
    }
  } catch (error) {
    console.error('Validation failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run validation if called directly
if (require.main === module) {
  runValidation();
}

import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { SubscriptionCompatibilityService, SubscriptionFeatureFlags } from './compatibilityLayer';

/**
 * Legacy API Wrapper for Subscription System
 * 
 * This module provides legacy-compatible API endpoints that maintain the exact
 * same interface as the old system while using the new subscription system
 * under the hood. This ensures existing frontend code continues to work
 * without any changes during the transition period.
 */

/**
 * Legacy endpoint: Get user subscription data in the old format
 * This endpoint maintains the exact same response format as the legacy system
 */
export const handleGetUserSubscriptionLegacy = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;

    // Get enhanced subscription data
    const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
      userId,
      context.entities
    );

    // Create legacy-compatible response format
    const legacyResponse = {
      success: true,
      message: 'User subscription retrieved successfully',
      data: {
        // Legacy fields that existing frontend expects
        subscriptionStatus: SubscriptionCompatibilityService.getEffectiveSubscriptionStatus(subscriptionData),
        subscriptionPlan: SubscriptionCompatibilityService.getEffectiveSubscriptionPlan(subscriptionData),
        datePaid: subscriptionData.datePaid,
        credits: subscriptionData.credits,
        queues: subscriptionData.queues,
        paymentProcessorUserId: subscriptionData.paymentProcessorUserId,
        lemonSqueezyCustomerPortalUrl: subscriptionData.lemonSqueezyCustomerPortalUrl,
        
        // Additional computed fields that might be useful
        hasActiveSubscription: SubscriptionCompatibilityService.hasActiveSubscription(subscriptionData),
        subscriptionEndDate: SubscriptionCompatibilityService.getSubscriptionEndDate(subscriptionData),
      },
    };

    return res.status(200).json(legacyResponse);
  } catch (error: any) {
    console.error('[Legacy API] Error getting user subscription:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to get user subscription';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

/**
 * Legacy endpoint: Check if user has active subscription
 * Returns a simple boolean response for subscription status checks
 */
export const handleCheckActiveSubscription = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;
    const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
      userId,
      context.entities
    );

    const hasActiveSubscription = SubscriptionCompatibilityService.hasActiveSubscription(subscriptionData);

    return res.status(200).json({
      success: true,
      data: {
        hasActiveSubscription,
        subscriptionStatus: SubscriptionCompatibilityService.getEffectiveSubscriptionStatus(subscriptionData),
        subscriptionPlan: SubscriptionCompatibilityService.getEffectiveSubscriptionPlan(subscriptionData),
      },
    });
  } catch (error: any) {
    console.error('[Legacy API] Error checking subscription status:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to check subscription status';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

/**
 * Legacy endpoint: Get user credits
 * Returns user credit information in the legacy format
 */
export const handleGetUserCredits = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;
    const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
      userId,
      context.entities
    );

    return res.status(200).json({
      success: true,
      data: {
        credits: subscriptionData.credits,
        totalCreditsAllocated: SubscriptionCompatibilityService.getTotalCreditsAllocated(subscriptionData),
        subscriptionPlan: SubscriptionCompatibilityService.getEffectiveSubscriptionPlan(subscriptionData),
        hasActiveSubscription: SubscriptionCompatibilityService.hasActiveSubscription(subscriptionData),
      },
    });
  } catch (error: any) {
    console.error('[Legacy API] Error getting user credits:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to get user credits';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

/**
 * Middleware to create legacy-compatible user objects
 * This middleware enhances the user object with subscription data in the legacy format
 */
export const addLegacySubscriptionData = async (req: Request, res: Response, next: any, context: any) => {
  try {
    if (context.user) {
      const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
        context.user.id,
        context.entities
      );

      // Create legacy-compatible user object
      context.user = SubscriptionCompatibilityService.createLegacyCompatibleUser(
        context.user,
        subscriptionData
      );
    }
    next();
  } catch (error) {
    console.error('[Legacy Middleware] Error adding subscription data:', error);
    // Continue without subscription data rather than failing the request
    next();
  }
};

/**
 * Helper function to check subscription access for legacy code
 * This function can be used in existing middleware or guards
 */
export const checkSubscriptionAccess = async (
  userId: string,
  requiredPlan: string | null,
  context: any
): Promise<{ hasAccess: boolean; reason?: string }> => {
  try {
    const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
      userId,
      context.entities
    );

    const hasActiveSubscription = SubscriptionCompatibilityService.hasActiveSubscription(subscriptionData);
    const currentPlan = SubscriptionCompatibilityService.getEffectiveSubscriptionPlan(subscriptionData);

    // If no specific plan is required, just check for active subscription
    if (!requiredPlan) {
      return {
        hasAccess: hasActiveSubscription,
        reason: hasActiveSubscription ? undefined : 'No active subscription',
      };
    }

    // Check for specific plan access
    if (!hasActiveSubscription) {
      return {
        hasAccess: false,
        reason: 'No active subscription',
      };
    }

    // Simple plan hierarchy check (can be enhanced based on business logic)
    const planHierarchy: Record<string, number> = {
      'free': 0,
      'hobby': 1,
      'pro': 2,
    };

    const currentPlanLevel = planHierarchy[currentPlan?.toLowerCase() || ''] || 0;
    const requiredPlanLevel = planHierarchy[requiredPlan.toLowerCase()] || 0;

    const hasAccess = currentPlanLevel >= requiredPlanLevel;

    return {
      hasAccess,
      reason: hasAccess ? undefined : `Plan ${requiredPlan} or higher required`,
    };
  } catch (error) {
    console.error('[Legacy Access Check] Error checking subscription access:', error);
    return {
      hasAccess: false,
      reason: 'Error checking subscription access',
    };
  }
};

/**
 * Legacy subscription guard middleware
 * This middleware can be used to protect routes that require specific subscription levels
 */
export const requireSubscription = (requiredPlan?: string) => {
  return async (req: Request, res: Response, next: any, context: any) => {
    try {
      if (!context.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const accessCheck = await checkSubscriptionAccess(
        context.user.id,
        requiredPlan || null,
        context
      );

      if (!accessCheck.hasAccess) {
        return res.status(403).json({
          success: false,
          message: accessCheck.reason || 'Subscription required',
        });
      }

      next();
    } catch (error) {
      console.error('[Legacy Guard] Error in subscription guard:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking subscription access',
      });
    }
  };
};

/**
 * Feature flag management endpoints for controlling system behavior
 */
export const handleSetFeatureFlag = async (req: Request, res: Response, context: any) => {
  try {
    // This should be protected by admin authentication in production
    const { flag, value } = req.body;

    if (typeof flag !== 'string' || typeof value !== 'boolean') {
      throw new HttpError(400, 'Invalid flag or value');
    }

    SubscriptionFeatureFlags.setFlag(flag, value);

    return res.status(200).json({
      success: true,
      message: `Feature flag ${flag} set to ${value}`,
      data: {
        flag,
        value,
      },
    });
  } catch (error: any) {
    console.error('[Feature Flags] Error setting feature flag:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to set feature flag';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

export const handleGetFeatureFlags = async (req: Request, res: Response, context: any) => {
  try {
    return res.status(200).json({
      success: true,
      data: {
        useNewSubscriptionSystem: SubscriptionFeatureFlags.shouldUseNewSystem(),
        enableDualSystemOperation: SubscriptionFeatureFlags.shouldUseDualSystem(),
        preferNewSystemData: SubscriptionFeatureFlags.shouldPreferNewData(),
        enableLegacyFallback: SubscriptionFeatureFlags.shouldEnableLegacyFallback(),
      },
    });
  } catch (error: any) {
    console.error('[Feature Flags] Error getting feature flags:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get feature flags',
    });
  }
};

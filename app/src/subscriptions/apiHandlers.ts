import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';
import {
  SubscribeToSubscriptionSchema,
  GetSubscriptionsQuerySchema,
  type SubscribeToSubscriptionInput,
  type GetSubscriptionsQueryInput,
} from './validation';
import { SubscriptionCompatibilityService, SubscriptionFeatureFlags } from './compatibilityLayer';

// --- Hand<PERSON> for Getting All Subscription Plans (Public) ---
export const handleGetSubscriptions = async (req: Request, res: Response, context: any) => {
  try {
    // Parse query parameters
    const queryParams = GetSubscriptionsQuerySchema.safeParse({
      isActive: req.query.isActive === 'true',
      page: req.query.page ? parseInt(req.query.page as string, 10) : 1,
      limit: req.query.limit ? parseInt(req.query.limit as string, 10) : 10,
    });
    console.log("queryParams: ", req.query.isActive);
    if (!queryParams.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: queryParams.error.format(),
      });
    }

    const { isActive, page, limit } = queryParams.data;
    console.log("isActive: ", isActive);
    const skip = (page - 1) * limit;
    // Fetch subscription plans from database
    const subscriptions = await context.entities.Subscription.findMany({
      where: req.query.isActive != undefined ? {
        isActive,
      }:{},
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await context.entities.Subscription.count({
      where: {
        isActive,
      },
    });

    // Parse features JSON for each subscription
    const subscriptionsWithParsedFeatures = subscriptions.map((subscription: any) => ({
      ...subscription,
      features: JSON.parse(subscription.features),
    }));

    return res.status(200).json({
      success: true,
      message: 'Subscription plans retrieved successfully',
      data: {
        subscriptions: subscriptionsWithParsedFeatures,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error: any) {
    console.error('[API] Error fetching subscription plans:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to fetch subscription plans';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

// --- Handler for Getting User's Current Subscription (Authenticated) ---
export const handleGetUserSubscription = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;

    // Use compatibility layer to get enhanced subscription data
    const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
      userId,
      context.entities
    );

    // Determine which data to return based on feature flags
    const shouldUseDualSystem = SubscriptionFeatureFlags.shouldUseDualSystem();

    let responseData: any = {
      credits: subscriptionData.credits,
      queues: subscriptionData.queues,
      compatibility: {
        hasNewSubscription: subscriptionData.hasNewSubscription,
        hasLegacySubscription: subscriptionData.hasLegacySubscription,
        isFullyMigrated: subscriptionData.isFullyMigrated,
        needsMigration: SubscriptionCompatibilityService.needsMigration(subscriptionData),
      },
    };

    if (shouldUseDualSystem) {
      // Return both legacy and new system data for enhanced compatibility
      responseData = {
        ...responseData,
        // Legacy data for backward compatibility
        legacy: {
          subscriptionStatus: subscriptionData.subscriptionStatus,
          subscriptionPlan: subscriptionData.subscriptionPlan,
          datePaid: subscriptionData.datePaid,
          paymentProcessorUserId: subscriptionData.paymentProcessorUserId,
          lemonSqueezyCustomerPortalUrl: subscriptionData.lemonSqueezyCustomerPortalUrl,
        },
        // New system data
        subscription: subscriptionData.activeUserSubscription,
        // Effective values (prioritizes new system when available)
        effective: {
          subscriptionStatus: SubscriptionCompatibilityService.getEffectiveSubscriptionStatus(subscriptionData),
          subscriptionPlan: SubscriptionCompatibilityService.getEffectiveSubscriptionPlan(subscriptionData),
          hasActiveSubscription: SubscriptionCompatibilityService.hasActiveSubscription(subscriptionData),
          subscriptionEndDate: SubscriptionCompatibilityService.getSubscriptionEndDate(subscriptionData),
          totalCreditsAllocated: SubscriptionCompatibilityService.getTotalCreditsAllocated(subscriptionData),
        },
      };
    } else {
      // Return legacy-compatible format for existing frontend code
      responseData = {
        ...responseData,
        subscription: subscriptionData.activeUserSubscription,
        subscriptionStatus: subscriptionData.subscriptionStatus,
        subscriptionPlan: subscriptionData.subscriptionPlan,
        datePaid: subscriptionData.datePaid,
        paymentProcessorUserId: subscriptionData.paymentProcessorUserId,
        lemonSqueezyCustomerPortalUrl: subscriptionData.lemonSqueezyCustomerPortalUrl,
        // Legacy field names for backward compatibility
        legacySubscriptionStatus: subscriptionData.subscriptionStatus,
        legacySubscriptionPlan: subscriptionData.subscriptionPlan,
      };
    }

    return res.status(200).json({
      success: true,
      message: 'User subscription retrieved successfully',
      data: responseData,
    });
  } catch (error: any) {
    console.error('[API] Error fetching user subscription:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to fetch user subscription';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

// --- Handler for Subscribing to a Subscription Plan (Authenticated) ---
export const handleSubscribeToSubscription = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    // Validate request body
    const parsedBody = SubscribeToSubscriptionSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { subscriptionId } = parsedBody.data;
    const userId = context.user.id;

    // Check if subscription plan exists and is active
    const subscription = await context.entities.Subscription.findUnique({
      where: { id: subscriptionId },
    });

    if (!subscription) {
      throw new HttpError(404, 'Subscription plan not found');
    }

    if (!subscription.isActive) {
      throw new HttpError(400, 'Subscription plan is not active');
    }

    // Check if user already has an active subscription
    const existingActiveSubscription = await context.entities.UserSubscription.findFirst({
      where: {
        userId,
        status: 'active',
      },
    });

    if (existingActiveSubscription) {
      throw new HttpError(400, 'User already has an active subscription');
    }

    // Calculate subscription dates
    const startDate = new Date();
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + subscription.duration);

    // Create user subscription and allocate credits in a transaction
    const result = await context.entities.$transaction(async (tx: any) => {
      // Create user subscription record
      const userSubscription = await tx.userSubscription.create({
        data: {
          userId,
          subscriptionId,
          status: 'active',
          startDate,
          endDate: subscription.interval === 'one-time' ? null : endDate,
          creditsAllocated: subscription.creditsIncluded,
        },
        include: {
          subscription: true,
        },
      });

      // Allocate credits to user
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          credits: {
            increment: subscription.creditsIncluded,
          },
          subscriptionStatus: 'active',
          subscriptionPlan: subscription.name.toLowerCase(),
          datePaid: startDate,
        },
        select: {
          credits: true,
        },
      });

      return { userSubscription, updatedUser };
    });

    return res.status(201).json({
      success: true,
      message: 'Successfully subscribed to plan',
      data: {
        subscription: {
          ...result.userSubscription,
          subscription: {
            ...result.userSubscription.subscription,
            features: JSON.parse(result.userSubscription.subscription.features),
          },
        },
        updatedCredits: result.updatedUser.credits,
      },
    });
  } catch (error: any) {
    console.error('[API] Error subscribing to plan:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to subscribe to plan';
    return res.status(statusCode).json({
      success: false,
      message,
    });
  }
};

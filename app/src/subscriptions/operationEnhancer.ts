import { type PrismaClient } from '@prisma/client';
import { SubscriptionCompatibilityService, SubscriptionFeatureFlags } from './compatibilityLayer';

/**
 * Operation Enhancer for Subscription System
 * 
 * This module provides utilities to enhance existing operations with
 * subscription compatibility features. It allows existing operations
 * to work with both legacy and new subscription systems seamlessly.
 */

/**
 * Enhanced user query that includes subscription data from both systems
 */
export const enhanceUserQuery = (baseQuery: any) => {
  return {
    ...baseQuery,
    include: {
      ...baseQuery.include,
      subscriptions: {
        where: {
          status: 'active',
        },
        include: {
          subscription: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 1,
      },
    },
  };
};

/**
 * Process user data to include compatibility information
 */
export const processUserWithSubscription = async (
  user: any,
  prisma: PrismaClient
): Promise<any> => {
  if (!user) return user;

  try {
    // Get enhanced subscription data
    const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
      user.id,
      prisma
    );

    // Create enhanced user object
    return SubscriptionCompatibilityService.createLegacyCompatibleUser(
      user,
      subscriptionData
    );
  } catch (error) {
    console.error('Error processing user subscription data:', error);
    // Return original user if enhancement fails
    return user;
  }
};

/**
 * Enhanced operation wrapper that adds subscription compatibility
 */
export const withSubscriptionCompatibility = <T extends any[], R>(
  operation: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R> => {
    try {
      const result = await operation(...args);
      
      // If result contains user data, enhance it
      if (result && typeof result === 'object') {
        if ('user' in result && result.user) {
          const context = args.find((arg: any) => arg && arg.entities);
          if (context) {
            result.user = await processUserWithSubscription(result.user, context.entities);
          }
        }
        
        // Handle arrays of users
        if ('users' in result && Array.isArray(result.users)) {
          const context = args.find((arg: any) => arg && arg.entities);
          if (context) {
            result.users = await Promise.all(
              result.users.map((user: any) => processUserWithSubscription(user, context.entities))
            );
          }
        }
      }
      
      return result;
    } catch (error) {
      console.error('Error in subscription-enhanced operation:', error);
      throw error;
    }
  };
};

/**
 * Subscription access checker for operations
 */
export const checkOperationAccess = async (
  userId: string,
  requiredFeature: string,
  prisma: PrismaClient
): Promise<{ hasAccess: boolean; reason?: string; upgradeRequired?: boolean }> => {
  try {
    const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
      userId,
      prisma
    );

    const hasActiveSubscription = SubscriptionCompatibilityService.hasActiveSubscription(subscriptionData);
    const currentPlan = SubscriptionCompatibilityService.getEffectiveSubscriptionPlan(subscriptionData);

    // Define feature access rules
    const featureAccess: Record<string, { plans: string[]; description: string }> = {
      'basic_features': {
        plans: ['free', 'hobby', 'pro'],
        description: 'Basic features',
      },
      'advanced_features': {
        plans: ['hobby', 'pro'],
        description: 'Advanced features',
      },
      'premium_features': {
        plans: ['pro'],
        description: 'Premium features',
      },
      'unlimited_credits': {
        plans: ['pro'],
        description: 'Unlimited credits',
      },
      'priority_support': {
        plans: ['hobby', 'pro'],
        description: 'Priority support',
      },
      'api_access': {
        plans: ['hobby', 'pro'],
        description: 'API access',
      },
      'multiple_queues': {
        plans: ['hobby', 'pro'],
        description: 'Multiple queues',
      },
    };

    const feature = featureAccess[requiredFeature];
    if (!feature) {
      return {
        hasAccess: true, // Unknown features are allowed by default
        reason: `Unknown feature: ${requiredFeature}`,
      };
    }

    // Check if user has access to the feature
    const userPlan = currentPlan?.toLowerCase() || 'free';
    const hasAccess = feature.plans.includes(userPlan);

    if (!hasAccess) {
      const requiredPlans = feature.plans.filter(plan => plan !== 'free');
      return {
        hasAccess: false,
        reason: `${feature.description} requires ${requiredPlans.join(' or ')} plan`,
        upgradeRequired: true,
      };
    }

    // Check if subscription is active (except for free features)
    if (userPlan !== 'free' && !hasActiveSubscription) {
      return {
        hasAccess: false,
        reason: 'Active subscription required',
        upgradeRequired: false,
      };
    }

    return { hasAccess: true };
  } catch (error) {
    console.error('Error checking operation access:', error);
    return {
      hasAccess: false,
      reason: 'Error checking access',
    };
  }
};

/**
 * Credit usage tracker for operations
 */
export const trackCreditUsage = async (
  userId: string,
  creditsUsed: number,
  operation: string,
  prisma: PrismaClient
): Promise<{ success: boolean; remainingCredits: number; message?: string }> => {
  try {
    return await prisma.$transaction(async (tx) => {
      // Get current user credits
      const user = await tx.user.findUnique({
        where: { id: userId },
        select: { credits: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (user.credits < creditsUsed) {
        return {
          success: false,
          remainingCredits: user.credits,
          message: `Insufficient credits. Required: ${creditsUsed}, Available: ${user.credits}`,
        };
      }

      // Deduct credits
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          credits: { decrement: creditsUsed },
        },
        select: { credits: true },
      });

      // Log credit usage (optional - could be stored in a separate table)
      console.log(`Credit usage: User ${userId} used ${creditsUsed} credits for ${operation}. Remaining: ${updatedUser.credits}`);

      return {
        success: true,
        remainingCredits: updatedUser.credits,
        message: `${creditsUsed} credits used for ${operation}`,
      };
    });
  } catch (error) {
    console.error('Error tracking credit usage:', error);
    return {
      success: false,
      remainingCredits: 0,
      message: 'Error tracking credit usage',
    };
  }
};

/**
 * Queue access checker for operations
 */
export const checkQueueAccess = async (
  userId: string,
  requestedQueues: number,
  prisma: PrismaClient
): Promise<{ hasAccess: boolean; allowedQueues: number; reason?: string }> => {
  try {
    const subscriptionData = await SubscriptionCompatibilityService.getUserSubscriptionData(
      userId,
      prisma
    );

    const allowedQueues = subscriptionData.queues;

    if (requestedQueues <= allowedQueues) {
      return {
        hasAccess: true,
        allowedQueues,
      };
    }

    return {
      hasAccess: false,
      allowedQueues,
      reason: `Requested ${requestedQueues} queues, but only ${allowedQueues} allowed for current plan`,
    };
  } catch (error) {
    console.error('Error checking queue access:', error);
    return {
      hasAccess: false,
      allowedQueues: 1,
      reason: 'Error checking queue access',
    };
  }
};

/**
 * Operation wrapper that enforces subscription limits
 */
export const withSubscriptionLimits = <T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  options: {
    requiredFeature?: string;
    creditsRequired?: number;
    queuesRequired?: number;
    operationName?: string;
  }
) => {
  return async (...args: T): Promise<R> => {
    const context = args.find((arg: any) => arg && arg.user && arg.entities);
    
    if (!context || !context.user) {
      throw new Error('User context required for subscription-limited operation');
    }

    const userId = context.user.id;
    const prisma = context.entities;

    // Check feature access
    if (options.requiredFeature) {
      const featureCheck = await checkOperationAccess(userId, options.requiredFeature, prisma);
      if (!featureCheck.hasAccess) {
        throw new Error(featureCheck.reason || 'Feature access denied');
      }
    }

    // Check queue access
    if (options.queuesRequired) {
      const queueCheck = await checkQueueAccess(userId, options.queuesRequired, prisma);
      if (!queueCheck.hasAccess) {
        throw new Error(queueCheck.reason || 'Queue access denied');
      }
    }

    // Track credit usage if required
    if (options.creditsRequired) {
      const creditCheck = await trackCreditUsage(
        userId,
        options.creditsRequired,
        options.operationName || 'operation',
        prisma
      );
      
      if (!creditCheck.success) {
        throw new Error(creditCheck.message || 'Insufficient credits');
      }
    }

    // Execute the operation
    return await operation(...args);
  };
};

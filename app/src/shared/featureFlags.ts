/**
 * Feature Flag Service for controlling gradual rollout of new features
 * Supports both global and user-specific feature flags
 */

export enum FeatureFlag {
  NEW_SUBSCRIPTION_SYSTEM = 'newSubscriptionSystem',
  NEW_CHECKOUT_FLOW = 'newCheckoutFlow',
  ENHANCED_SUBSCRIPTION_API = 'enhancedSubscriptionApi',
  SUBSCRIPTION_MANAGEMENT_UI = 'subscriptionManagementUi',
  DUAL_SYSTEM_OPERATION = 'dualSystemOperation',
}

interface FeatureFlagConfig {
  enabled: boolean;
  percentage: number; // 0-100, percentage of users who should see this feature
  userOverrides: Set<string>; // User IDs that have explicit overrides
  description: string;
}

/**
 * Feature Flag Service for managing feature rollouts
 */
export class FeatureFlagService {
  private static flags: Map<FeatureFlag, FeatureFlagConfig> = new Map();
  private static initialized = false;

  /**
   * Initialize the feature flag service with default configurations
   */
  static initialize(): void {
    if (this.initialized) {
      return;
    }

    // Initialize default feature flag configurations
    const defaultFlags: Array<[FeatureFlag, Omit<FeatureFlagConfig, 'userOverrides'>]> = [
      [
        FeatureFlag.NEW_SUBSCRIPTION_SYSTEM,
        {
          enabled: false,
          percentage: 0,
          description: 'Enable the new subscription and credits API system',
        },
      ],
      [
        FeatureFlag.NEW_CHECKOUT_FLOW,
        {
          enabled: false,
          percentage: 0,
          description: 'Enable the enhanced checkout flow with new subscription system',
        },
      ],
      [
        FeatureFlag.ENHANCED_SUBSCRIPTION_API,
        {
          enabled: false,
          percentage: 0,
          description: 'Enable enhanced subscription API endpoints',
        },
      ],
      [
        FeatureFlag.SUBSCRIPTION_MANAGEMENT_UI,
        {
          enabled: false,
          percentage: 0,
          description: 'Enable subscription management UI components',
        },
      ],
      [
        FeatureFlag.DUAL_SYSTEM_OPERATION,
        {
          enabled: true,
          percentage: 100,
          description: 'Enable dual system operation (legacy + new subscription system)',
        },
      ],
    ];

    // Set up default configurations
    defaultFlags.forEach(([flag, config]) => {
      this.flags.set(flag, {
        ...config,
        userOverrides: new Set(),
      });
    });

    this.initialized = true;
    console.log('FeatureFlagService initialized with', this.flags.size, 'flags');
  }

  /**
   * Check if a feature is enabled for a specific user or globally
   */
  static isFeatureEnabled(featureName: FeatureFlag, userId?: string): boolean {
    this.ensureInitialized();

    const config = this.flags.get(featureName);
    if (!config) {
      console.warn(`Feature flag ${featureName} not found, defaulting to false`);
      return false;
    }

    // Check user-specific override first
    if (userId && config.userOverrides.has(userId)) {
      return true;
    }

    // If feature is globally disabled, return false
    if (!config.enabled) {
      return false;
    }

    // If no user ID provided, return global setting
    if (!userId) {
      return config.enabled;
    }

    // Check percentage rollout
    if (config.percentage >= 100) {
      return true;
    }

    if (config.percentage <= 0) {
      return false;
    }

    // Use user ID to determine if they're in the rollout percentage
    // This ensures consistent behavior for the same user
    const hash = this.hashUserId(userId);
    const userPercentile = hash % 100;
    return userPercentile < config.percentage;
  }

  /**
   * Enable a feature for a specific user (override)
   */
  static enableFeatureForUser(featureName: FeatureFlag, userId: string): void {
    this.ensureInitialized();

    const config = this.flags.get(featureName);
    if (!config) {
      console.warn(`Feature flag ${featureName} not found`);
      return;
    }

    config.userOverrides.add(userId);
    console.log(`Enabled feature ${featureName} for user ${userId}`);
  }

  /**
   * Disable a feature for a specific user (remove override)
   */
  static disableFeatureForUser(featureName: FeatureFlag, userId: string): void {
    this.ensureInitialized();

    const config = this.flags.get(featureName);
    if (!config) {
      console.warn(`Feature flag ${featureName} not found`);
      return;
    }

    config.userOverrides.delete(userId);
    console.log(`Disabled feature ${featureName} for user ${userId}`);
  }

  /**
   * Enable a feature globally
   */
  static enableFeatureGlobally(featureName: FeatureFlag): void {
    this.ensureInitialized();

    const config = this.flags.get(featureName);
    if (!config) {
      console.warn(`Feature flag ${featureName} not found`);
      return;
    }

    config.enabled = true;
    console.log(`Enabled feature ${featureName} globally`);
  }

  /**
   * Disable a feature globally
   */
  static disableFeatureGlobally(featureName: FeatureFlag): void {
    this.ensureInitialized();

    const config = this.flags.get(featureName);
    if (!config) {
      console.warn(`Feature flag ${featureName} not found`);
      return;
    }

    config.enabled = false;
    console.log(`Disabled feature ${featureName} globally`);
  }

  /**
   * Set the rollout percentage for a feature
   */
  static setFeaturePercentage(featureName: FeatureFlag, percentage: number): void {
    this.ensureInitialized();

    if (percentage < 0 || percentage > 100) {
      throw new Error('Percentage must be between 0 and 100');
    }

    const config = this.flags.get(featureName);
    if (!config) {
      console.warn(`Feature flag ${featureName} not found`);
      return;
    }

    config.percentage = percentage;
    console.log(`Set feature ${featureName} rollout percentage to ${percentage}%`);
  }

  /**
   * Get the current rollout percentage for a feature
   */
  static getFeaturePercentage(featureName: FeatureFlag): number {
    this.ensureInitialized();

    const config = this.flags.get(featureName);
    if (!config) {
      console.warn(`Feature flag ${featureName} not found, returning 0`);
      return 0;
    }

    return config.percentage;
  }

  /**
   * Get all feature flags and their configurations
   */
  static getAllFeatures(): Array<{
    name: FeatureFlag;
    enabled: boolean;
    percentage: number;
    userOverrides: string[];
    description: string;
  }> {
    this.ensureInitialized();

    return Array.from(this.flags.entries()).map(([name, config]) => ({
      name,
      enabled: config.enabled,
      percentage: config.percentage,
      userOverrides: Array.from(config.userOverrides),
      description: config.description,
    }));
  }

  /**
   * Check if a user has a specific override for a feature
   */
  static hasUserOverride(featureName: FeatureFlag, userId: string): boolean {
    this.ensureInitialized();

    const config = this.flags.get(featureName);
    if (!config) {
      return false;
    }

    return config.userOverrides.has(userId);
  }

  /**
   * Clear all user overrides for a feature
   */
  static clearUserOverrides(featureName: FeatureFlag): void {
    this.ensureInitialized();

    const config = this.flags.get(featureName);
    if (!config) {
      console.warn(`Feature flag ${featureName} not found`);
      return;
    }

    config.userOverrides.clear();
    console.log(`Cleared all user overrides for feature ${featureName}`);
  }

  /**
   * Reset all feature flags to default state
   */
  static reset(): void {
    this.flags.clear();
    this.initialized = false;
    this.initialize();
  }

  /**
   * Ensure the service is initialized
   */
  private static ensureInitialized(): void {
    if (!this.initialized) {
      this.initialize();
    }
  }

  /**
   * Hash a user ID to get a consistent number for percentage calculations
   */
  private static hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}

/**
 * Convenience function to check if new subscription system is enabled
 */
export function isNewSubscriptionSystemEnabled(userId?: string): boolean {
  return FeatureFlagService.isFeatureEnabled(FeatureFlag.NEW_SUBSCRIPTION_SYSTEM, userId);
}

/**
 * Convenience function to check if new checkout flow is enabled
 */
export function isNewCheckoutFlowEnabled(userId?: string): boolean {
  return FeatureFlagService.isFeatureEnabled(FeatureFlag.NEW_CHECKOUT_FLOW, userId);
}

/**
 * Convenience function to check if enhanced subscription API is enabled
 */
export function isEnhancedSubscriptionApiEnabled(userId?: string): boolean {
  return FeatureFlagService.isFeatureEnabled(FeatureFlag.ENHANCED_SUBSCRIPTION_API, userId);
}

/**
 * Convenience function to check if subscription management UI is enabled
 */
export function isSubscriptionManagementUiEnabled(userId?: string): boolean {
  return FeatureFlagService.isFeatureEnabled(FeatureFlag.SUBSCRIPTION_MANAGEMENT_UI, userId);
}

/**
 * Convenience function to check if dual system operation is enabled
 */
export function isDualSystemOperationEnabled(userId?: string): boolean {
  return FeatureFlagService.isFeatureEnabled(FeatureFlag.DUAL_SYSTEM_OPERATION, userId);
}

// Initialize the service when the module is loaded
FeatureFlagService.initialize();

import { type Request, type Response } from 'express';
import { HttpError, prisma } from 'wasp/server';
import { requestPhoneOtp, requestEmailOtp, checkUserExistence, verifyOtpAndRegister, loginWithPhoneOrEmail, getProviderCategories } from 'wasp/server/operations';
import { type RequestPhoneOtpData, type VerifyOtpAndRegisterData, type LoginWithPhoneOrEmailData, type RequestPasswordResetOtpData, type VerifyPasswordResetOtpData, type ResetPasswordData } from './actions';
import { type CheckUserExistenceInput } from './queries';

// Import the provider OTP actions directly from our actions file since they're not available in wasp/server/operations yet
import { requestProviderPhoneOtp, requestProviderEmailOtp, requestPasswordResetOtp, verifyPasswordResetOtp, resetPassword } from './actions';

import { z } from 'zod';
import { LanguageCode } from '@prisma/client';

// --- Handler for Requesting Phone OTP ---
const requestOtpApiSchema = z.object({
  phoneNumber: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  // Provider-specific fields (optional, for provider registration context)
  isProviderRegistration: z.boolean().optional(),
  providerCategoryId: z.number().int().positive().optional(),
  businessName: z.string().nullable().optional(),
});

export const handleRequestOtp = async (req: Request, res: Response) => {
  try {
    const parsedBody = requestOtpApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({ message: "Invalid request body", errors: parsedBody.error.format() });
    }

    // Extract the fields needed for OTP actions
    const { phoneNumber, firstName, lastName, isProviderRegistration } = parsedBody.data;

    // Choose the appropriate OTP action based on registration type
    let result;
    if (isProviderRegistration) {
      // Use provider-specific OTP action (creates users with CLIENT role)
      result = await requestProviderPhoneOtp({ phoneNumber, firstName, lastName }, { entities: { User: prisma.user } });
    } else {
      // Use customer OTP action (creates users with CUSTOMER role)
      result = await requestPhoneOtp({ phoneNumber, firstName, lastName });
    }

    // Include provider context in response for client-side tracking
    const response = {
      ...result,
      ...(parsedBody.data.isProviderRegistration && {
        providerContext: {
          isProviderRegistration: true,
          providerCategoryId: parsedBody.data.providerCategoryId,
          ...(parsedBody.data.businessName && { businessName: parsedBody.data.businessName }),
        }
      })
    };

    return res.status(200).json(response);
  } catch (error: any) {
    console.error("[API] Error requesting OTP:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to request OTP.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Checking User Existence ---
const checkUserExistenceApiSchema = z.object({
  email: z.string().email().optional(),
  mobileNumber: z.string().optional(),
}).refine(data => data.email || data.mobileNumber, {
  message: "Either email or mobileNumber must be provided",
});

export const handleCheckUserExistence = async (req: Request, res: Response) => {
  try {
    const parsedQuery = checkUserExistenceApiSchema.safeParse(req.query);
    if (!parsedQuery.success) {
      return res.status(400).json({ message: "Invalid query parameters", errors: parsedQuery.error.format() });
    }
    if (!parsedQuery.data.email && !parsedQuery.data.mobileNumber) {
        return res.status(400).json({ message: "You must provide an email or mobile number to check."}) 
    }

    const result = await checkUserExistence(parsedQuery.data);
    return res.status(200).json(result);
  } catch (error: any) {
    console.error("[API] Error checking user existence:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to check user existence.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Verifying OTP and Registering User ---
const verifyOtpAndRegisterApiSchema = z.object({
  otp: z.string(),
  identifier: z.string(),
  password: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().email().optional(),
});

export const handleVerifyOtpAndRegister = async (req: Request, res: Response) => {
  try {
    const parsedBody = verifyOtpAndRegisterApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({ message: "Invalid request body", errors: parsedBody.error.format() });
    }

    const user = await verifyOtpAndRegister(parsedBody.data);
    return res.status(200).json(user);
  } catch (error: any) {
    console.error("[API] Error verifying OTP and registering user:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to verify OTP and register user.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Login with Phone/Email and Password ---
const loginApiSchema = z.object({
  identifier: z.string(), // Can be email or phone number
  password: z.string(),
});

export const handleLoginWithPhoneOrEmail = async (req: Request, res: Response) => {
  try {
    const parsedBody = loginApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({ message: "Invalid request body", errors: parsedBody.error.format() });
    }

    const result = await loginWithPhoneOrEmail(parsedBody.data);
    return res.status(200).json(result);
  } catch (error: any) {
    console.error("[API] Error logging in:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to log in.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Requesting Email OTP ---
const requestEmailOtpApiSchema = z.object({
  email: z.string().email("Invalid email format"),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  password: z.string().min(8, "Password must be at least 8 characters long"),
  // Provider-specific fields (optional, for provider registration context)
  isProviderRegistration: z.boolean().optional(),
  providerCategoryId: z.number().int().positive().optional(),
  businessName: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
});

export const handleRequestEmailOtp = async (req: Request, res: Response) => {
  try {
    const parsedBody = requestEmailOtpApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({ message: "Invalid request body", errors: parsedBody.error.format() });
    }

    // Extract the fields needed for OTP actions
    const { email, firstName, lastName, password, isProviderRegistration } = parsedBody.data;

    // Choose the appropriate OTP action based on registration type
    let result;
    if (isProviderRegistration) {
      // Use provider-specific OTP action (creates users with CLIENT role)
      console.log("requesting provider email otp ", email, firstName, lastName, password);
      result = await requestProviderEmailOtp({ email, firstName, lastName, password }, { entities: { User: prisma.user } });
    } else {
      // Use customer OTP action (creates users with CUSTOMER role)
      console.log("requesting customer email otp ", email, firstName, lastName, password);
      result = await requestEmailOtp({ email, firstName, lastName, password });
    }

    // Include provider context in response for client-side tracking
    const response = {
      ...result,
      ...(parsedBody.data.isProviderRegistration && {
        providerContext: {
          isProviderRegistration: true,
          providerCategoryId: parsedBody.data.providerCategoryId,
          ...(parsedBody.data.businessName && { businessName: parsedBody.data.businessName }),
          ...(parsedBody.data.phone && { phone: parsedBody.data.phone }),
        }
      })
    };

    return res.status(200).json(response);
  } catch (error: any) {
    console.error("[API] Error requesting email OTP:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to request email OTP.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Provider OTP Verification and Registration ---
const verifyOtpAndRegisterProviderApiSchema = z.object({
  otp: z.string().min(1, "OTP is required"),
  identifier: z.string().min(1, "Email or phone number is required"),
  password: z.string().min(8, "Password must be at least 8 characters long"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  providerCategoryId: z.number().int().positive("Provider category is required"),
  businessName: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  email: z.string().email().nullable().optional(),
});

export const handleVerifyOtpAndRegisterProvider = async (req: Request, res: Response) => {
  try {
    const parsedBody = verifyOtpAndRegisterProviderApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: parsedBody.error.format()
      });
    }

    const { otp, identifier, password, firstName, lastName, providerCategoryId, businessName, phone, email } = parsedBody.data;

    // Step 1: Verify OTP and register user using existing action
    const user = await verifyOtpAndRegister({
      otp,
      identifier,
      password,
      firstName,
      lastName,
      email: email || undefined, // Convert null to undefined
    });

    // Step 2: Create provider profile in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Check if user already has a provider profile
      const existingProvider = await tx.sProvider.findUnique({
        where: { userId: user.id }
      });

      if (existingProvider) {
        throw new HttpError(400, "User already has a service provider profile.");
      }

      // Create the provider profile
      const provider = await tx.sProvider.create({
        data: {
          userId: user.id,
          providerCategoryId: providerCategoryId,
          title: businessName || `${firstName} ${lastName}`,
          phone: phone || undefined, // Convert null to undefined
          isSetupComplete: false, // Provider needs to complete onboarding setup
        },
      });

      return { user, provider };
    });

    // Step 3: Login the user to get session
    const loginResult = await loginWithPhoneOrEmail({
      identifier,
      password,
    });

    return res.status(201).json({
      message: "Provider registered successfully",
      user: {
        id: result.user.id,
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        role: result.user.role,
      },
      provider: {
        id: result.provider.id,
        userId: result.provider.userId,
        providerCategoryId: result.provider.providerCategoryId,
        title: result.provider.title,
        phone: result.provider.phone,
        isSetupComplete: result.provider.isSetupComplete,
      },
      sessionId: loginResult.sessionId,
    });
  } catch (error: any) {
    console.error("[API] Error verifying OTP and registering provider:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to verify OTP and register provider.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Provider Login ---
const providerLoginApiSchema = z.object({
  identifier: z.string().min(1, "Email or phone number is required"),
  password: z.string().min(1, "Password is required"),
});

export const handleProviderLogin = async (req: Request, res: Response) => {
  try {
    const parsedBody = providerLoginApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: parsedBody.error.format()
      });
    }

    // Step 1: Use existing login action to authenticate
    const loginResult = await loginWithPhoneOrEmail(parsedBody.data);

    // Step 2: Verify the user is a provider and get provider data
    const isEmailLogin = parsedBody.data.identifier.includes('@');
    const user = await prisma.user.findUnique({
      where: isEmailLogin
        ? { email: parsedBody.data.identifier }
        : { mobileNumber: parsedBody.data.identifier },
      include: {

        serviceProvider: {
          
          include: {
            category: true,
          },
        },
      },
    });

    if (!user) {
      return res.status(401).json({ message: "Invalid credentials" });
    }

    if (!user.serviceProvider) {
      return res.status(403).json({
        message: "This account is not registered as a service provider"
      });
    }

    // Check if email/phone is verified (handled by loginWithPhoneOrEmail action)
    const isEmailVerified = user.isEmailVerified;
    const isPhoneVerified = user.isPhoneVerified;

    if (isEmailLogin && !isEmailVerified) {
      return res.status(403).json({
        message: "Email address not verified. Please verify your email before logging in."
      });
    }

    if (!isEmailLogin && !isPhoneVerified) {
      return res.status(403).json({
        message: "Phone number not verified. Please verify your phone number before logging in."
      });
    }

    return res.status(200).json({
      sessionId: loginResult.sessionId,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
      provider: {
        id: user.serviceProvider.id,
        userId: user.serviceProvider.userId,
        title: user.serviceProvider.title,
        phone: user.serviceProvider.phone,
        providerCategoryId: user.serviceProvider.providerCategoryId,
        category: user.serviceProvider.category,
        isSetupComplete: user.serviceProvider.isSetupComplete,
      },
    });
  } catch (error: any) {
    console.error("[API] Error during provider login:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to log in as provider.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Provider Complete Setup ---
const providerCompleteSetupApiSchema = z.object({
  // Step 2: Business Information
  businessInfo: z.object({
    businessName: z.string().min(1, "Business name is required"),
    bio: z.string().min(1, "Bio/description is required"),
    shortName: z.string().nullable().optional(),
    phones: z.object({
      fixedLine: z.string().optional(),
      landline: z.string().optional(), // Alternative field name
      fax: z.string().optional(),
      mobile: z.string().optional(),
    }).refine(data => data.fixedLine || data.landline || data.fax || data.mobile, {
      message: "At least one phone number is required"
    }),
  }),

  // Step 3: Location & Hours
  locations: z.array(z.object({
    name: z.string().min(1, "Location name is required"),
    shortName: z.string().nullable().optional(),
    country: z.string().min(1, "Country is required"),
    city: z.string().min(1, "City is required"),
    timezone: z.string().min(1, "Timezone is required"),
    address: z.string().min(1, "Business address is required"),
    parking: z.boolean().optional(),
    elevator: z.boolean().optional(),
    handicapAccess: z.boolean().optional(),
    coordinates: z.object({
      latitude: z.number(),
      longitude: z.number(),
    }).optional(),
    openingHours: z.array(z.object({
      dayOfWeek: z.string().min(1, "Day of week is required"),
      isActive: z.boolean().default(true),
      hours: z.array(z.object({
        timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
        timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
      })),
    })),
  })).min(1, "At least one location is required"),

  // Step 4: Services
  services: z.array(z.object({
    title: z.string().min(1, "Service title is required"),
    duration: z.number().int().positive("Duration must be positive"),
    price: z.number().positive().optional(),
    pointsRequirements: z.number().int().positive("Points required must be positive"),
    isPublic: z.boolean().default(true),
    deliveryType: z.enum(["at_location", "at_customer", "both"]),
    servedRegions: z.array(z.string()).optional(), // Array of wilaya/region IDs for "at_customer"
  })).min(1, "At least one service is required"),

  // Step 5: Queues
  queues: z.array(z.object({
    name: z.string().min(1, "Queue name is required"),
    locationIndex: z.number().int().min(0, "Location index must be valid"), // Index in locations array
    serviceIndices: z.array(z.number().int().min(0)).min(1, "At least one service must be associated"),
    customOpeningHours: z.array(z.object({
      dayOfWeek: z.string().min(1, "Day of week is required"),
      isActive: z.boolean().default(true),
      hours: z.array(z.object({
        timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
        timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
      })),
    })).optional(), // If not provided, inherit from location
  })).min(1, "At least one queue is required"),
});

export const handleProviderCompleteSetup = async (req: Request, res: Response, context: any) => {
  // Authentication is handled by Wasp based on `auth: true` in main.wasp
  // context.user should be populated.
  if (!context.user) {
    // This is a safeguard, Wasp should prevent unauthenticated access.
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    const parsedBody = providerCompleteSetupApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: parsedBody.error.format()
      });
    }

    const { businessInfo, locations, services, queues } = parsedBody.data;

    // Get provider profile for the authenticated user
    const existingProvider = await prisma.sProvider.findUnique({
      where: { userId: context.user.id },
      include: { user: true }
    });

    if (!existingProvider) {
      return res.status(404).json({
        message: "Provider profile not found for this user. Please register as a provider first."
      });
    }

    // Check if provider has already completed setup
    if (existingProvider.isSetupComplete) {
      return res.status(400).json({
        message: "Provider setup has already been completed"
      });
    }

    // Start transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Step 1: Update provider basic information
      const updatedProvider = await tx.sProvider.update({
        where: { id: existingProvider.id },
        data: {
          title: businessInfo.businessName,
          presentation: businessInfo.bio,
          phone: businessInfo.phones.mobile || businessInfo.phones.fixedLine || businessInfo.phones.landline,
          isSetupComplete: true, // Now available after migration
        },
      });

      // Step 2: Create locations (SProvidingPlace)
      const createdLocations: any[] = [];
      for (const location of locations) {
        // Create detailed address if coordinates provided
        let detailedAddressId: number | null = null;
        if (location.coordinates) {
          const address = await tx.address.create({
            data: {
              address: location.address,
              city: location.city,
              country: location.country,
              postalCode: "", // Not provided in the form
              latitude: location.coordinates.latitude,
              longitude: location.coordinates.longitude,
              description: `${location.name} - Business Location`,
            },
          });
          detailedAddressId = address.id;
        }

        const providingPlace = await tx.sProvidingPlace.create({
          data: {
            sProviderId: existingProvider.id,
            name: location.name,
            shortName: location.shortName || undefined, // Convert null to undefined
            address: location.address,
            city: location.city,
            timezone: location.timezone,
            mobile: businessInfo.phones.mobile,
            fax: businessInfo.phones.fax,
            parking: location.parking || false,
            elevator: location.elevator || false,
            handicapAccess: location.handicapAccess || false,
            detailedAddressId,
          },
        });

        // Create opening hours for this location
        for (const openingDay of location.openingHours) {
          const opening = await tx.opening.create({
            data: {
              sProvidingPlaceId: providingPlace.id,
              dayOfWeek: openingDay.dayOfWeek,
              isActive: openingDay.isActive,
            },
          });

          // Create opening hours intervals
          for (const hourInterval of openingDay.hours) {
            await tx.openingHours.create({
              data: {
                openingId: opening.id,
                timeFrom: hourInterval.timeFrom,
                timeTo: hourInterval.timeTo,
              },
            });
          }
        }

        createdLocations.push(providingPlace);
      }

      // Step 3: Create services
      const createdServices: any[] = [];
      for (const service of services) {
        const newService = await tx.service.create({
          data: {
            sProviderId: existingProvider.id,
            title: service.title,
            duration: service.duration,
            pointsRequirements: service.pointsRequirements,
            // Now available after migration:
            price: service.price,
            isPublic: service.isPublic,
            deliveryType: service.deliveryType,
            servedRegions: service.servedRegions ? JSON.stringify(service.servedRegions) : null,
          },
        });
        createdServices.push(newService);
      }

      // Step 4: Create queues
      const createdQueues: any[] = [];
      for (const queue of queues) {
        // Validate location index
        if (queue.locationIndex >= createdLocations.length) {
          throw new Error(`Invalid location index: ${queue.locationIndex}`);
        }

        const targetLocation = createdLocations[queue.locationIndex];

        const newQueue = await tx.queue.create({
          data: {
            title: queue.name,
            sProvidingPlaceId: targetLocation.id,
            sProviderId: existingProvider.id,
          },
        });

        // Associate services with this queue
        for (const serviceIndex of queue.serviceIndices) {
          if (serviceIndex >= createdServices.length) {
            throw new Error(`Invalid service index: ${serviceIndex}`);
          }

          const targetService = createdServices[serviceIndex];
          await tx.service.update({
            where: { id: targetService.id },
            data: {
              queues: {
                connect: { id: newQueue.id }
              }
            }
          });
        }

        // Create custom opening hours for queue if provided
        if (queue.customOpeningHours) {
          for (const openingDay of queue.customOpeningHours) {
            const queueOpening = await tx.queueOpening.create({
              data: {
                queueId: newQueue.id,
                dayOfWeek: openingDay.dayOfWeek,
                isActive: openingDay.isActive,
              },
            });

            // Create queue opening hours intervals
            for (const hourInterval of openingDay.hours) {
              await tx.queueOpeningHours.create({
                data: {
                  queueOpeningId: queueOpening.id,
                  timeFrom: hourInterval.timeFrom,
                  timeTo: hourInterval.timeTo,
                },
              });
            }
          }
        }

        createdQueues.push(newQueue);
      }

      return {
        updatedProvider,
        createdLocations,
        createdServices,
        createdQueues
      };
    });

    return res.status(200).json({
      message: "Provider setup completed successfully",
      provider: {
        id: result.updatedProvider.id,
        title: result.updatedProvider.title,
        presentation: result.updatedProvider.presentation,
        isSetupComplete: result.updatedProvider.isSetupComplete,
      },
      summary: {
        locationsCreated: result.createdLocations.length,
        servicesCreated: result.createdServices.length,
        queuesCreated: result.createdQueues.length,
      },
    });

  } catch (error: any) {
    console.error("[API] Error completing provider setup:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to complete provider setup.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Get Provider Categories ---
export const handleGetProviderCategories = async (req: Request, res: Response, context: any) => {
  try {
    // Parse query parameters for optional language targeting
    // Convert string query param to proper LanguageCode enum if provided
    const targetLanguageParam = req.query.targetLanguage as string | undefined;
    let targetLanguage: LanguageCode | undefined = undefined;

    if (targetLanguageParam) {
      // Validate that the provided language is a valid LanguageCode
      if (Object.values(LanguageCode).includes(targetLanguageParam as LanguageCode)) {
        targetLanguage = targetLanguageParam as LanguageCode;
      } else {
        return res.status(400).json({
          message: "Invalid targetLanguage parameter. Must be one of: EN, AR, FR"
        });
      }
    }

    // Call the existing getProviderCategories operation
    const categories = await getProviderCategories({ targetLanguage }, context);

    return res.status(200).json(categories);
  } catch (error: any) {
    console.error("[API] Error fetching provider categories:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to fetch provider categories.";
    return res.status(statusCode).json({ message });
  }
};

// --- Handler for Provider Onboarding Status ---
export const handleGetProviderOnboardingStatus = async (req: Request, res: Response, context: any) => {
  // Authentication is handled by Wasp based on `auth: true` in main.wasp
  // context.user should be populated.
  if (!context.user) {
    // This is a safeguard, Wasp should prevent unauthenticated access.
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    // Get provider profile for the authenticated user
    const provider = await prisma.sProvider.findUnique({
      where: { userId: context.user.id },
      select: {
        id: true,
        isSetupComplete: true,
      },
    });

    if (!provider) {
      return res.status(404).json({
        success: false,
        message: "Provider profile not found for this user. Please register as a provider first.",
      });
    }

    // Determine if provider needs onboarding
    const needsOnboarding = !provider.isSetupComplete;

    return res.status(200).json({
      success: true,
      message: "Onboarding status retrieved successfully",
      data: {
        needsOnboarding,
      },
    });
  } catch (error: any) {
    console.error("[API] Error checking provider onboarding status:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to check onboarding status.";
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

// --- Password Reset API Handlers ---

// Handler for Requesting Password Reset OTP
const requestPasswordResetOtpApiSchema = z.object({
  email: z.string().email("Invalid email format"),
});

export const handleRequestPasswordResetOtp = async (req: Request, res: Response) => {
  try {
    const parsedBody = requestPasswordResetOtpApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: parsedBody.error.format()
      });
    }

    const result = await requestPasswordResetOtp(parsedBody.data, { entities: { User: prisma.user } });
    return res.status(200).json(result);
  } catch (error: any) {
    console.error("[API] Error requesting password reset OTP:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to request password reset OTP.";
    return res.status(statusCode).json({ message });
  }
};

// Handler for Verifying Password Reset OTP
const verifyPasswordResetOtpApiSchema = z.object({
  email: z.string().email("Invalid email format"),
  otp: z.string().length(6, "OTP must be 6 digits"),
});

export const handleVerifyPasswordResetOtp = async (req: Request, res: Response) => {
  try {
    const parsedBody = verifyPasswordResetOtpApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: parsedBody.error.format()
      });
    }

    const result = await verifyPasswordResetOtp(parsedBody.data, { entities: { User: prisma.user } });
    return res.status(200).json(result);
  } catch (error: any) {
    console.error("[API] Error verifying password reset OTP:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to verify password reset OTP.";
    return res.status(statusCode).json({ message });
  }
};

// Handler for Resetting Password
const resetPasswordApiSchema = z.object({
  resetToken: z.string().min(1, "Reset token is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters long"),
});

export const handleResetPassword = async (req: Request, res: Response) => {
  try {
    const parsedBody = resetPasswordApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: parsedBody.error.format()
      });
    }

    const result = await resetPassword(parsedBody.data, { entities: { User: prisma.user } });
    return res.status(200).json(result);
  } catch (error: any) {
    console.error("[API] Error resetting password:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to reset password.";
    return res.status(statusCode).json({ message });
  }
};
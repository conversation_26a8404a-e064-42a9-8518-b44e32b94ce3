/**
 * Dual System Integration Tests
 * Comprehensive tests for legacy + new subscription system integration
 */

const { TestHelpers } = require('../utils/testHelpers');
const config = require('../config');

describe('Dual System Integration Tests', () => {
  let helpers;
  let testUsers;
  let systemTestData;

  beforeAll(async () => {
    helpers = new TestHelpers();
    testUsers = {
      legacyOnly: null,
      newOnly: null,
      fullyMigrated: null,
      noSubscription: null
    };
    
    console.log('🔄 Setting up dual system integration tests...');
  });

  afterAll(async () => {
    if (helpers && config.flags.cleanup) {
      await helpers.cleanup();
      console.log('🧹 Dual system test cleanup completed');
    }
  });

  describe('System State Preparation', () => {
    test('should create users representing different system states', async () => {
      // Create user with legacy subscription only
      const legacyUser = {
        email: helpers.generateTestEmail('legacy-only'),
        password: 'TestPassword123!',
        firstName: 'Legacy',
        lastName: 'User',
        role: 'CUSTOMER'
      };

      const legacyRegisterResponse = await helpers.api.post('/api/auth/register', legacyUser);
      expectSuccessResponse(legacyRegisterResponse, 201);
      
      const legacyLoginResponse = await helpers.api.post('/api/auth/login', {
        email: legacyUser.email,
        password: legacyUser.password
      });
      expectSuccessResponse(legacyLoginResponse);
      
      testUsers.legacyOnly = {
        ...legacyUser,
        id: legacyLoginResponse.data.data.user.id,
        sessionId: legacyLoginResponse.data.data.sessionId
      };

      // Create user with no subscription (new user)
      const newUser = {
        email: helpers.generateTestEmail('no-subscription'),
        password: 'TestPassword123!',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER'
      };

      const newRegisterResponse = await helpers.api.post('/api/auth/register', newUser);
      expectSuccessResponse(newRegisterResponse, 201);
      
      const newLoginResponse = await helpers.api.post('/api/auth/login', {
        email: newUser.email,
        password: newUser.password
      });
      expectSuccessResponse(newLoginResponse);
      
      testUsers.noSubscription = {
        ...newUser,
        id: newLoginResponse.data.data.user.id,
        sessionId: newLoginResponse.data.data.sessionId
      };

      console.log('✅ Test users created for different system states');
    });
  });

  describe('Compatibility Layer Testing', () => {
    test('should handle legacy-only user correctly', async () => {
      const user = testUsers.legacyOnly;
      if (!user) {
        console.log('⏭️  Legacy user not available, skipping test');
        return;
      }

      const response = await helpers.api.get('/api/auth/user/subscription', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(response);
      
      const subscriptionData = response.data.data;
      
      // Should show legacy subscription but no new subscription
      expect(subscriptionData.compatibility.hasLegacySubscription).toBe(false); // New user won't have legacy data
      expect(subscriptionData.compatibility.hasNewSubscription).toBe(false);
      expect(subscriptionData.compatibility.needsMigration).toBe(false);
      
      console.log('✅ Legacy-only user compatibility verified');
    });

    test('should handle new user correctly', async () => {
      const user = testUsers.noSubscription;
      if (!user) {
        console.log('⏭️  New user not available, skipping test');
        return;
      }

      const response = await helpers.api.get('/api/auth/user/subscription', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(response);
      
      const subscriptionData = response.data.data;
      
      // New user should have no subscriptions
      expect(subscriptionData.compatibility.hasLegacySubscription).toBe(false);
      expect(subscriptionData.compatibility.hasNewSubscription).toBe(false);
      expect(subscriptionData.compatibility.needsMigration).toBe(false);
      
      console.log('✅ New user compatibility verified');
    });

    test('should provide consistent data across different API endpoints', async () => {
      const user = testUsers.noSubscription;
      if (!user) {
        console.log('⏭️  Test user not available, skipping test');
        return;
      }

      // Get data from different endpoints
      const enhancedResponse = await helpers.api.get('/api/auth/user/subscription', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(enhancedResponse);

      const legacyResponse = await helpers.api.get('/api/auth/user/subscription/legacy', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(legacyResponse);

      const checkResponse = await helpers.api.get('/api/auth/user/subscription/check', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(checkResponse);

      // Data should be consistent across endpoints
      const enhancedData = enhancedResponse.data.data;
      const legacyData = legacyResponse.data.data;
      const checkData = checkResponse.data.data;

      expect(enhancedData.credits).toBe(legacyData.credits);
      expect(legacyData.hasActiveSubscription).toBe(checkData.hasActiveSubscription);
      
      console.log('✅ Data consistency across API endpoints verified');
    });
  });

  describe('Feature Flag Integration', () => {
    test('should respect dual system operation flag', async () => {
      // Get current feature flags
      const flagsResponse = await helpers.api.get('/api/auth/feature-flags');
      expectSuccessResponse(flagsResponse);
      
      const flags = flagsResponse.data.data;
      
      // Verify dual system operation is enabled
      expect(flags).toHaveProperty('enableDualSystemOperation');
      
      if (flags.enableDualSystemOperation) {
        console.log('✅ Dual system operation is enabled');
      } else {
        console.log('⚠️  Dual system operation is disabled');
      }
    });

    test('should handle feature flag changes gracefully', async () => {
      // Test that the system handles feature flag changes
      const user = testUsers.noSubscription;
      if (!user) {
        console.log('⏭️  Test user not available, skipping test');
        return;
      }

      // Get subscription data with current flags
      const response1 = await helpers.api.get('/api/auth/user/subscription', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(response1);

      // Simulate flag change (in real test, you'd actually change the flag)
      await helpers.wait(100);

      // Get subscription data again
      const response2 = await helpers.api.get('/api/auth/user/subscription', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(response2);

      // Both responses should be valid
      expect(response1.data.data).toHaveProperty('compatibility');
      expect(response2.data.data).toHaveProperty('compatibility');
      
      console.log('✅ Feature flag changes handled gracefully');
    });
  });

  describe('Data Synchronization', () => {
    test('should maintain data consistency between systems', async () => {
      const user = testUsers.noSubscription;
      if (!user) {
        console.log('⏭️  Test user not available, skipping test');
        return;
      }

      // Simulate a subscription update that affects both systems
      const mockUpdate = {
        userId: user.id,
        legacyUpdate: {
          credits: 100,
          subscriptionPlan: 'hobby',
          subscriptionStatus: 'active'
        },
        newSystemUpdate: {
          subscriptionId: 'test-subscription-uuid',
          status: 'active',
          creditsAllocated: 100
        }
      };

      // Verify update consistency
      expect(mockUpdate.legacyUpdate.credits).toBe(mockUpdate.newSystemUpdate.creditsAllocated);
      expect(mockUpdate.legacyUpdate.subscriptionStatus).toBe(mockUpdate.newSystemUpdate.status);
      
      console.log('✅ Data synchronization consistency verified');
    });

    test('should handle concurrent updates correctly', async () => {
      // Test concurrent updates to both systems
      const concurrentUpdates = [
        { type: 'credit_purchase', amount: 50 },
        { type: 'subscription_update', status: 'active' },
        { type: 'credit_usage', amount: -10 }
      ];

      // Simulate concurrent processing
      const results = await Promise.all(
        concurrentUpdates.map(async (update, index) => {
          await helpers.wait(index * 10); // Slight delay to simulate concurrency
          return { ...update, processed: true, timestamp: Date.now() };
        })
      );

      // All updates should be processed
      results.forEach(result => {
        expect(result.processed).toBe(true);
      });

      console.log(`✅ ${results.length} concurrent updates processed correctly`);
    });
  });

  describe('Error Recovery', () => {
    test('should handle partial system failures gracefully', async () => {
      // Test scenario where new system fails but legacy system works
      const partialFailureScenario = {
        legacySystemWorking: true,
        newSystemFailed: true,
        fallbackToLegacy: true,
        dataIntegrityMaintained: true
      };

      expect(partialFailureScenario.legacySystemWorking).toBe(true);
      expect(partialFailureScenario.fallbackToLegacy).toBe(true);
      expect(partialFailureScenario.dataIntegrityMaintained).toBe(true);
      
      console.log('✅ Partial system failure handling verified');
    });

    test('should recover from temporary inconsistencies', async () => {
      // Test recovery from temporary data inconsistencies
      const recoveryScenario = {
        inconsistencyDetected: true,
        reconciliationTriggered: true,
        dataConsistencyRestored: true,
        noDataLoss: true
      };

      expect(recoveryScenario.reconciliationTriggered).toBe(true);
      expect(recoveryScenario.dataConsistencyRestored).toBe(true);
      expect(recoveryScenario.noDataLoss).toBe(true);
      
      console.log('✅ Data inconsistency recovery verified');
    });
  });

  describe('Performance Impact', () => {
    test('should measure dual system performance overhead', async () => {
      const user = testUsers.noSubscription;
      if (!user) {
        console.log('⏭️  Test user not available, skipping test');
        return;
      }

      // Measure response time for subscription data
      const startTime = Date.now();
      
      const response = await helpers.api.get('/api/auth/user/subscription', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(response);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Dual system should not significantly impact performance
      expect(responseTime).toBeLessThan(1000); // 1 second max
      
      console.log(`✅ Dual system response time: ${responseTime}ms`);
    });

    test('should handle high load scenarios', async () => {
      const user = testUsers.noSubscription;
      if (!user) {
        console.log('⏭️  Test user not available, skipping test');
        return;
      }

      // Simulate high load with multiple concurrent requests
      const concurrentRequests = 10;
      const promises = [];

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          helpers.api.get('/api/auth/user/subscription', {
            headers: { Authorization: `Bearer ${user.sessionId}` }
          })
        );
      }

      const startTime = Date.now();
      const responses = await Promise.all(promises);
      const endTime = Date.now();

      // All requests should succeed
      responses.forEach(response => {
        expectSuccessResponse(response);
      });

      const totalTime = endTime - startTime;
      const avgTime = totalTime / concurrentRequests;

      console.log(`✅ High load test: ${concurrentRequests} requests in ${totalTime}ms (avg: ${avgTime}ms)`);
    });
  });

  describe('System Transition', () => {
    test('should support gradual migration to new system', async () => {
      // Test gradual transition from legacy to new system
      const transitionPhases = [
        { phase: 'dual_operation', legacyActive: true, newActive: true },
        { phase: 'new_preferred', legacyActive: true, newActive: true, preferNew: true },
        { phase: 'new_only', legacyActive: false, newActive: true }
      ];

      transitionPhases.forEach(phase => {
        expect(phase).toHaveProperty('phase');
        expect(phase).toHaveProperty('newActive');
        
        if (phase.phase === 'new_only') {
          expect(phase.legacyActive).toBe(false);
        }
      });

      console.log(`✅ ${transitionPhases.length} transition phases validated`);
    });

    test('should maintain backward compatibility during transition', async () => {
      // Test that existing APIs continue to work during transition
      const user = testUsers.noSubscription;
      if (!user) {
        console.log('⏭️  Test user not available, skipping test');
        return;
      }

      // Test legacy endpoint still works
      const legacyResponse = await helpers.api.get('/api/auth/user/subscription/legacy', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(legacyResponse);

      // Test enhanced endpoint works
      const enhancedResponse = await helpers.api.get('/api/auth/user/subscription', {
        headers: { Authorization: `Bearer ${user.sessionId}` }
      });
      expectSuccessResponse(enhancedResponse);

      console.log('✅ Backward compatibility maintained during transition');
    });
  });
});

/**
 * Subscription System Integration Tests
 * Tests for the dual subscription system (legacy + new UserSubscription)
 */

const { TestHelpers } = require('../utils/testHelpers');
const config = require('../config');

describe('Subscription System Integration Tests', () => {
  let helpers;
  let testUser;
  let testSubscriptionData;

  beforeAll(async () => {
    helpers = new TestHelpers();
    
    // Create test user for subscription tests
    testUser = {
      email: helpers.generateTestEmail('subscription'),
      password: 'TestPassword123!',
      firstName: 'Subscription',
      lastName: 'Tester',
      role: 'CUSTOMER'
    };

    console.log('🧪 Setting up subscription system integration tests...');
  });

  afterAll(async () => {
    if (helpers && config.flags.cleanup) {
      await helpers.cleanup();
      console.log('🧹 Subscription test cleanup completed');
    }
  });

  describe('Subscription Plans Management', () => {
    test('should retrieve all available subscription plans', async () => {
      const response = await helpers.api.get('/api/subscriptions');
      
      expectSuccessResponse(response);
      const plans = response.data.data;
      
      expect(Array.isArray(plans)).toBe(true);
      expect(plans.length).toBeGreaterThan(0);
      
      // Validate plan structure
      plans.forEach(plan => {
        expect(plan).toHaveProperty('id');
        expect(plan).toHaveProperty('name');
        expect(plan).toHaveProperty('description');
        expect(plan).toHaveProperty('price');
        expect(plan).toHaveProperty('creditsIncluded');
        expect(plan).toHaveProperty('features');
        expect(plan).toHaveProperty('isActive');
      });

      console.log(`✅ Found ${plans.length} subscription plans`);
    });

    test('should filter active subscription plans', async () => {
      const response = await helpers.api.get('/api/subscriptions?isActive=true');
      
      expectSuccessResponse(response);
      const activePlans = response.data.data;
      
      activePlans.forEach(plan => {
        expect(plan.isActive).toBe(true);
      });

      console.log(`✅ Found ${activePlans.length} active subscription plans`);
    });
  });

  describe('User Subscription Management', () => {
    beforeAll(async () => {
      // Register test user
      const registerResponse = await helpers.api.post('/api/auth/register', testUser);
      expectSuccessResponse(registerResponse, 201);
      
      // Login to get session
      const loginResponse = await helpers.api.post('/api/auth/login', {
        email: testUser.email,
        password: testUser.password
      });
      expectSuccessResponse(loginResponse);
      
      testUser.sessionId = loginResponse.data.data.sessionId;
      testUser.id = loginResponse.data.data.user.id;
      
      // Set auth header for subsequent requests
      helpers.api.defaults.headers.common['Authorization'] = `Bearer ${testUser.sessionId}`;
      
      console.log(`✅ Test user created and authenticated: ${testUser.email}`);
    });

    test('should get user subscription data with compatibility layer', async () => {
      const response = await helpers.api.get('/api/auth/user/subscription');
      
      expectSuccessResponse(response);
      const subscriptionData = response.data.data;
      
      // Check compatibility structure
      expect(subscriptionData).toHaveProperty('credits');
      expect(subscriptionData).toHaveProperty('compatibility');
      expect(subscriptionData.compatibility).toHaveProperty('hasNewSubscription');
      expect(subscriptionData.compatibility).toHaveProperty('hasLegacySubscription');
      expect(subscriptionData.compatibility).toHaveProperty('isFullyMigrated');
      expect(subscriptionData.compatibility).toHaveProperty('needsMigration');
      
      // For new user, should have no subscriptions initially
      expect(subscriptionData.compatibility.hasNewSubscription).toBe(false);
      expect(subscriptionData.compatibility.hasLegacySubscription).toBe(false);
      
      console.log('✅ User subscription compatibility data retrieved');
    });

    test('should get user subscription in legacy format', async () => {
      const response = await helpers.api.get('/api/auth/user/subscription/legacy');
      
      expectSuccessResponse(response);
      const legacyData = response.data.data;
      
      // Check legacy format
      expect(legacyData).toHaveProperty('credits');
      expect(legacyData).toHaveProperty('subscriptionStatus');
      expect(legacyData).toHaveProperty('subscriptionPlan');
      expect(legacyData).toHaveProperty('hasActiveSubscription');
      
      console.log('✅ Legacy subscription format working');
    });

    test('should check subscription access for features', async () => {
      const response = await helpers.api.get('/api/auth/user/subscription/check');
      
      expectSuccessResponse(response);
      const accessData = response.data.data;
      
      expect(accessData).toHaveProperty('hasActiveSubscription');
      expect(accessData).toHaveProperty('subscriptionStatus');
      expect(accessData).toHaveProperty('subscriptionPlan');
      
      console.log('✅ Subscription access check working');
    });
  });

  describe('Subscription Checkout Flow', () => {
    test('should generate checkout session with metadata', async () => {
      // Get available plans first
      const plansResponse = await helpers.api.get('/api/subscriptions');
      const plans = plansResponse.data.data;
      const testPlan = plans.find(plan => plan.isActive && plan.price > 0);
      
      if (!testPlan) {
        console.log('⏭️  No paid plans available, skipping checkout test');
        return;
      }

      const checkoutResponse = await helpers.api.post('/api/auth/checkout', {
        planId: testPlan.id
      });
      
      expectSuccessResponse(checkoutResponse);
      const checkoutData = checkoutResponse.data.data;
      
      expect(checkoutData).toHaveProperty('sessionUrl');
      expect(checkoutData).toHaveProperty('sessionId');
      expect(checkoutData.sessionUrl).toContain('http');
      
      console.log(`✅ Checkout session generated for plan: ${testPlan.name}`);
    });
  });

  describe('Feature Flag Management', () => {
    test('should get current feature flags', async () => {
      const response = await helpers.api.get('/api/auth/feature-flags');
      
      expectSuccessResponse(response);
      const flags = response.data.data;
      
      expect(flags).toHaveProperty('useNewSubscriptionSystem');
      expect(flags).toHaveProperty('enableDualSystemOperation');
      expect(flags).toHaveProperty('preferNewSystemData');
      expect(flags).toHaveProperty('enableLegacyFallback');
      
      console.log('✅ Feature flags retrieved');
    });

    test('should update feature flags (admin only)', async () => {
      try {
        const response = await helpers.api.post('/api/auth/feature-flags', {
          flag: 'enableDualSystemOperation',
          value: true
        });
        
        expectSuccessResponse(response);
        console.log('✅ Feature flag updated (admin access confirmed)');
      } catch (error) {
        if (error.response?.status === 403) {
          console.log('⏭️  Feature flag update requires admin access (expected for regular user)');
        } else {
          throw error;
        }
      }
    });
  });

  describe('Subscription Validation', () => {
    test('should validate subscription data consistency', async () => {
      // This test checks if the compatibility layer works correctly
      const response = await helpers.api.get('/api/auth/user/subscription');
      expectSuccessResponse(response);
      
      const data = response.data.data;
      
      // Validate compatibility flags consistency
      if (data.compatibility.hasNewSubscription && data.compatibility.hasLegacySubscription) {
        expect(data.compatibility.isFullyMigrated).toBe(true);
        expect(data.compatibility.needsMigration).toBe(false);
      }
      
      if (!data.compatibility.hasNewSubscription && data.compatibility.hasLegacySubscription) {
        expect(data.compatibility.needsMigration).toBe(true);
      }
      
      console.log('✅ Subscription data consistency validated');
    });

    test('should handle subscription access control', async () => {
      // Test basic feature access (should work for all users)
      try {
        const response = await helpers.api.get('/api/auth/user/credits');
        expectSuccessResponse(response);
        
        const creditData = response.data.data;
        expect(creditData).toHaveProperty('credits');
        expect(typeof creditData.credits).toBe('number');
        
        console.log(`✅ User has ${creditData.credits} credits`);
      } catch (error) {
        console.log('⚠️  Credit access endpoint may not be implemented');
      }
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid subscription plan requests', async () => {
      try {
        await helpers.api.post('/api/auth/checkout', {
          planId: 'invalid-plan-id'
        });
        
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error.response.status).toBe(400);
        console.log('✅ Invalid plan ID properly rejected');
      }
    });

    test('should handle unauthenticated subscription requests', async () => {
      // Remove auth header temporarily
      const originalAuth = helpers.api.defaults.headers.common['Authorization'];
      delete helpers.api.defaults.headers.common['Authorization'];
      
      try {
        await helpers.api.get('/api/auth/user/subscription');
        
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error.response.status).toBe(401);
        console.log('✅ Unauthenticated request properly rejected');
      } finally {
        // Restore auth header
        helpers.api.defaults.headers.common['Authorization'] = originalAuth;
      }
    });
  });

  describe('Performance Tests', () => {
    test('should handle subscription data retrieval efficiently', async () => {
      const startTime = Date.now();
      
      const response = await helpers.api.get('/api/auth/user/subscription');
      expectSuccessResponse(response);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Should respond within reasonable time (adjust threshold as needed)
      expect(responseTime).toBeLessThan(2000);
      
      console.log(`✅ Subscription data retrieved in ${responseTime}ms`);
    });

    test('should handle multiple concurrent subscription requests', async () => {
      const concurrentRequests = 5;
      const promises = [];
      
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(helpers.api.get('/api/auth/user/subscription'));
      }
      
      const startTime = Date.now();
      const responses = await Promise.all(promises);
      const endTime = Date.now();
      
      // All requests should succeed
      responses.forEach(response => {
        expectSuccessResponse(response);
      });
      
      const totalTime = endTime - startTime;
      console.log(`✅ ${concurrentRequests} concurrent requests completed in ${totalTime}ms`);
    });
  });
});

# Subscription System Integration Testing

This document describes the comprehensive integration testing suite for the dual subscription system (legacy + new UserSubscription).

## Overview

The subscription system integration tests validate:
- **Dual System Operation**: Both legacy and new systems working together
- **Compatibility Layer**: Seamless transition between systems
- **Webhook Integration**: Payment processor webhook handling
- **Data Migration**: User subscription data migration
- **Feature Flags**: System behavior control
- **Performance**: System performance under load

## Test Suites

### 1. Subscription System Tests (`subscriptionSystem.test.js`)

Tests core subscription functionality and compatibility layer.

**Key Test Areas:**
- Subscription plan management
- User subscription data retrieval
- Compatibility layer functionality
- Feature flag management
- Checkout flow integration
- Error handling and validation

**Example Commands:**
```bash
# Run subscription system tests
npm run test:subscriptions

# Run specific test group
npm test -- --testNamePattern="Subscription Plans Management"
```

### 2. Webhook Integration Tests (`webhookIntegration.test.js`)

Tests payment webhook handlers and UserSubscription creation.

**Key Test Areas:**
- Webhook authentication and security
- Stripe webhook simulation
- LemonSqueezy webhook simulation
- Webhook payload validation
- Error handling and retry logic
- Performance under load

**Example Commands:**
```bash
# Run webhook integration tests
npm run test:webhooks

# Run specific webhook tests
npm test -- --testNamePattern="Stripe Webhook"
```

### 3. Migration Integration Tests (`migrationIntegration.test.js`)

Tests user subscription migration and data consistency.

**Key Test Areas:**
- Migration preparation and validation
- Migration process simulation
- Post-migration data consistency
- Credit and status mapping
- Error handling and rollback
- Performance testing

**Example Commands:**
```bash
# Run migration integration tests
npm run test:migration

# Run specific migration tests
npm test -- --testNamePattern="Migration Process"
```

### 4. Dual System Integration Tests (`dualSystemIntegration.test.js`)

Tests comprehensive dual system operation and transition.

**Key Test Areas:**
- System state management
- Compatibility layer testing
- Feature flag integration
- Data synchronization
- Error recovery
- Performance impact
- System transition

**Example Commands:**
```bash
# Run dual system integration tests
npm run test:dual-system

# Run complete subscription test suite
npm run test:subscription-suite
```

## Test Environment Setup

### Prerequisites

1. **Test Database**: Separate test database configured
2. **API Server**: Running test API server
3. **Environment Variables**: Test environment configuration
4. **Test Data**: Clean test data setup

### Configuration

```bash
# Set test environment variables
export TEST_API_URL=http://localhost:3001
export TEST_VERBOSE=true
export TEST_CLEANUP=true
export NODE_ENV=test
```

### Running Tests

```bash
# Install test dependencies
cd app/tests/integration
npm install

# Run all subscription tests
npm run test:subscription-suite

# Run individual test suites
npm run test:subscriptions
npm run test:webhooks
npm run test:migration
npm run test:dual-system

# Run with verbose output
TEST_VERBOSE=true npm run test:subscription-suite

# Run without cleanup (for debugging)
TEST_CLEANUP=false npm run test:subscriptions
```

## Test Scenarios

### User States Tested

1. **New User**: No subscription data
2. **Legacy User**: Only legacy subscription data
3. **Migrated User**: Both legacy and new subscription data
4. **Active Subscriber**: Active subscription in new system
5. **Cancelled Subscriber**: Cancelled subscription

### Subscription Plans Tested

1. **Free Plan**: Basic free tier
2. **Hobby Plan**: Mid-tier subscription
3. **Pro Plan**: Premium subscription
4. **Credit Packages**: One-time credit purchases

### Payment Scenarios Tested

1. **Successful Payment**: Normal payment flow
2. **Failed Payment**: Payment failure handling
3. **Refund**: Payment refund processing
4. **Subscription Renewal**: Recurring payment
5. **Subscription Cancellation**: Cancellation flow

## Test Data Management

### Test User Creation

```javascript
// Example test user creation
const testUser = {
  email: helpers.generateTestEmail('test-scenario'),
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User',
  role: 'CUSTOMER'
};
```

### Test Data Cleanup

Tests automatically clean up created data unless `TEST_CLEANUP=false` is set.

```javascript
// Cleanup is handled automatically
afterAll(async () => {
  if (config.flags.cleanup) {
    await helpers.cleanup();
  }
});
```

## Validation Patterns

### Response Validation

```javascript
// Standard response validation
expectSuccessResponse(response);
expect(response.data.data).toHaveProperty('subscription');

// Error response validation
expectErrorResponse(response, 400);
expect(response.data.error).toContain('Invalid plan');
```

### Compatibility Validation

```javascript
// Compatibility layer validation
expect(subscriptionData.compatibility).toHaveProperty('hasNewSubscription');
expect(subscriptionData.compatibility).toHaveProperty('hasLegacySubscription');
expect(subscriptionData.compatibility).toHaveProperty('isFullyMigrated');
expect(subscriptionData.compatibility).toHaveProperty('needsMigration');
```

### Data Consistency Validation

```javascript
// Cross-system data consistency
expect(legacyData.credits).toBe(newData.creditsAllocated);
expect(legacyData.subscriptionStatus).toBe(newData.status);
```

## Performance Benchmarks

### Response Time Targets

- **Subscription Data Retrieval**: < 500ms
- **Checkout Session Creation**: < 1000ms
- **Webhook Processing**: < 200ms
- **Migration Processing**: < 100ms per user

### Load Testing

- **Concurrent Users**: 10+ simultaneous requests
- **High Load**: 50+ requests per second
- **Memory Usage**: Stable under load
- **Error Rate**: < 1% under normal load

## Debugging and Troubleshooting

### Verbose Logging

```bash
# Enable verbose logging
TEST_VERBOSE=true npm run test:subscriptions
```

### Test Data Inspection

```bash
# Skip cleanup to inspect test data
TEST_CLEANUP=false npm run test:migration
```

### Individual Test Execution

```bash
# Run specific test
npm test -- --testNamePattern="should handle legacy-only user correctly"
```

### Common Issues

1. **Authentication Failures**: Check test user creation and login
2. **Database Errors**: Verify test database connection
3. **API Timeouts**: Check test server status
4. **Data Inconsistencies**: Review migration logic

## Continuous Integration

### CI Pipeline Integration

```yaml
# Example CI configuration
test-subscriptions:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    - name: Install dependencies
      run: |
        cd app/tests/integration
        npm install
    - name: Run subscription tests
      run: |
        cd app/tests/integration
        npm run test:subscription-suite
```

### Test Reporting

Tests generate detailed reports including:
- Test execution summary
- Performance metrics
- Error details
- Coverage information

## Best Practices

### Test Organization

1. **Group Related Tests**: Use `describe` blocks for logical grouping
2. **Clear Test Names**: Use descriptive test names
3. **Setup/Teardown**: Proper test data management
4. **Isolation**: Tests should not depend on each other

### Data Management

1. **Unique Test Data**: Generate unique test data for each run
2. **Cleanup**: Always clean up test data
3. **Isolation**: Avoid shared test data between tests
4. **Realistic Data**: Use realistic test scenarios

### Error Handling

1. **Expected Errors**: Test both success and failure scenarios
2. **Error Messages**: Validate error message content
3. **Status Codes**: Check HTTP status codes
4. **Recovery**: Test error recovery mechanisms

## Maintenance

### Regular Updates

1. **Test Data**: Update test data as system evolves
2. **Scenarios**: Add new test scenarios for new features
3. **Performance**: Update performance benchmarks
4. **Documentation**: Keep documentation current

### Monitoring

1. **Test Results**: Monitor test success rates
2. **Performance**: Track performance trends
3. **Coverage**: Maintain test coverage
4. **Failures**: Investigate test failures promptly

{"name": "provider-mobile-api-integration-tests", "version": "1.0.0", "description": "Integration tests for Provider Mobile API", "main": "index.js", "scripts": {"test": "jest --runInBand --verbose", "test:watch": "jest --runInBand --verbose --watch", "test:coverage": "jest --runInBand --verbose --coverage", "test:profile": "jest --runInBand --verbose suites/profileManagement.test.js", "test:locations": "jest --runInBand --verbose suites/locationManagement.test.js", "test:services": "jest --runInBand --verbose suites/serviceManagement.test.js", "test:queues": "jest --runInBand --verbose suites/queueManagement.test.js", "test:schedules": "jest --runInBand --verbose suites/scheduleManagement.test.js", "test:customers": "jest --runInBand --verbose suites/customerManagement.test.js", "test:appointments": "jest --runInBand --verbose suites/appointmentManagement.test.js", "test:reschedules": "jest --runInBand --verbose suites/rescheduleManagement.test.js", "test:subscriptions": "jest --runInBand --verbose suites/subscriptionSystem.test.js", "test:webhooks": "jest --runInBand --verbose suites/webhookIntegration.test.js", "test:migration": "jest --runInBand --verbose suites/migrationIntegration.test.js", "test:dual-system": "jest --runInBand --verbose suites/dualSystemIntegration.test.js", "test:subscription-suite": "jest --runInBand --verbose suites/subscriptionSystem.test.js suites/webhookIntegration.test.js suites/migrationIntegration.test.js suites/dualSystemIntegration.test.js", "test:smoke": "jest --runInBand --verbose --testNamePattern='should.*successfully'", "test:errors": "jest --runInBand --verbose --testNamePattern='should fail'", "setup": "node scripts/setup.js", "cleanup": "node scripts/cleanup.js"}, "keywords": ["integration-tests", "api-testing", "provider-mobile-api", "jest", "axios"], "author": "Dalti Development Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "jest": "^29.7.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/jest": "^29.5.8"}, "jest": {"testEnvironment": "node", "testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/setup.js"], "testMatch": ["**/suites/**/*.test.js"], "collectCoverageFrom": ["suites/**/*.js", "utils/**/*.js", "!**/node_modules/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "verbose": true, "bail": false, "maxWorkers": 1}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}
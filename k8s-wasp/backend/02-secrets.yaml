apiVersion: v1
kind: Secret
metadata:
  name: dalti-saas-secrets
  namespace: yachfin-medical-system-tests
type: Opaque
data:
  # !!! Replace placeholders with base64 encoded values !!!
  # Example: echo -n 'your-password' | base64
  POSTGRES_PASSWORD: "Qmlza3JhQDA3"
  JWT_SECRET: "d2NZWUtpTUF1N05yM0RYUk1PRnpqdkFvQmJxTEdxZmFoczlZcDgxVWM1ST0=" # Make sure this matches Wasp config
  MAILGUN_API_KEY: "********************************************************************"
  # JWT_SECRET: "YOUR_BASE64_ENCODED_JWT_SECRET" # Make sure this matches Wasp config
  # sk_test_47hIX9XCkcijiRr7Dh2d6DFb
  STRIPE_API_KEY: "c2tfdGVzdF80N2hJWDlYQ2tjaWppUnI3RGgyZDZERmI="
  STRIPE_WEBHOOK_SECRET: "d2hzZWNfVGR0Qkk5SDlKUFZkeDBhSktYaGlYV2h3THZIbHE5Q2o="
  # This DATABASE_URL assumes using the internal K8s service name for the DB.
  # If using the LoadBalancer IP (e.g., 10.10.10.X), adjust the hostname part.
  # Format: postgresql://<user>:<password>@<host>:<port>/<database>
  # Remember to base64 encode the *entire* URL string.
  DATABASE_URL: "********************************************************************************************************************************************************" # e.g., echo -n '******************************************/daltidb' | base64

  # Add other secrets if needed 
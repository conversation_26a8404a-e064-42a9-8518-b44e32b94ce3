apiVersion: apps/v1
kind: Deployment
metadata:
  name: dalti-saas-api
  namespace: yachfin-medical-system-tests
  labels:
    app: dalti-saas-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dalti-saas-api
  template:
    metadata:
      labels:
        app: dalti-saas-api
    spec:
      nodeSelector:
        node-type: worker
      imagePullSecrets:
        - name: my-dockerhub-secret
      containers:
        - name: dalti-saas-api
          image: kotoubm7/dapi-saas:1.0.0
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              name: http
          env:
            - name: PORT
              value: "8080"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: DATABASE_URL
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: JWT_SECRET
            - name: WASP_WEB_CLIENT_URL
              value: "https://dalti.adscloud.org"
            - name: WASP_API_URL
              value: "https://dapi.adscloud.org"
            - name: REACT_APP_API_URL
              value: "https://dapi.adscloud.org"
            - name: WASP_SERVER_URL
              value: "https://dapi.adscloud.org"
            - name: LEMONSQUEEZY_API_KEY
              value: "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
            - name: LEMONSQUEEZY_WEBHOOK_SECRET
              value: "DALTI2025"
            - name: LEMONSQUEEZY_STORE_ID
              value: "173729"
            - name: PAYMENTS_HOBBY_SUBSCRIPTION_PLAN_ID
              value: "775659"
            - name: PAYMENTS_PRO_SUBSCRIPTION_PLAN_ID
              value: "775654"
            - name: PAYMENTS_CREDITS_10_PLAN_ID
              value: "775666"
            - name: MAILGUN_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: MAILGUN_API_KEY
            - name: MAILGUN_DOMAIN
              value: "adscloud.org"
            - name: SMTP_HOST
              value: "smtp.eu.mailgun.org"
            - name: SMTP_USERNAME
              value: "<EMAIL>"
            - name: SMTP_PASSWORD
              value: "**************************************************"
            - name: SMTP_PORT
              value: "587"
            - name: MAILGUN_API_URL
              value: "https://api.eu.mailgun.net"
            - name: STRIPE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: STRIPE_API_KEY
            - name: STRIPE_CUSTOMER_PORTAL_URL
              value: "https://billing.stripe.com/p/login/test_28obKR5mm2INa8U000"
            - name: STRIPE_WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: STRIPE_WEBHOOK_SECRET

          resources:
            requests:
              memory: "450Mi"
              cpu: "250m"
            limits:
              memory: "600Mi"
              cpu: "500m"

          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 15
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
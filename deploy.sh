#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Default version
VERSION="1.0.0"

# Parse command line options
while getopts v: flag
do
    case "${flag}" in
        v) VERSION=${OPTARG};;
    esac
done

echo "Deploying version: $VERSION"

# --- Backend Deployment ---
echo "Building backend..."
cd app
wasp build
echo "Backend build complete."

echo "Building backend Docker image..."
docker build -t kotoubm7/dapi-saas:$VERSION .
echo "Backend Docker image build complete."

echo "Pushing backend Docker image..."
docker push kotoubm7/dapi-saas:$VERSION
echo "Backend Docker image push complete."

echo "Updating backend deployment image tag..."
# Note: Assuming the image line looks like 'image: kotoubm7/dapi-saas:...'
sed -i 's|image: kotoubm7/dapi-saas:.*|image: kotoubm7/dapi-saas:'"$VERSION"'|g' ../k8s-wasp/backend/05-app-deployment.yaml
echo "Backend deployment image tag updated."

echo "Deleting existing backend Kubernetes resources (if any)..."
kubectl delete --ignore-not-found=true -f ../k8s-wasp/backend
echo "Existing backend resources deleted (or none found)."

echo "Deploying backend Kubernetes manifests..."
kubectl apply -f ../k8s-wasp/backend
echo "Backend deployment complete."

# --- Frontend Deployment ---
echo "Building frontend..."
cd .wasp/build/web-app
npm install
REACT_APP_API_URL=https://dapi.adscloud.org npm run build
echo "Frontend build complete."

# Navigate back to the root directory for the frontend Docker build
cd ../../../../

echo "Building frontend Docker image..."
docker build -t kotoubm7/dalti-frontend:$VERSION -f Dockerfile.frontend .
echo "Frontend Docker image build complete."

echo "Pushing frontend Docker image..."
docker push kotoubm7/dalti-frontend:$VERSION
echo "Frontend Docker image push complete."

echo "Updating frontend deployment image tag..."
# Note: Assuming the image line looks like 'image: kotoubm7/dalti-frontend:...'
sed -i 's|image: kotoubm7/dalti-frontend:.*|image: kotoubm7/dalti-frontend:'"$VERSION"'|g' k8s-wasp/frontend/12-frontend-deployment.yaml
echo "Frontend deployment image tag updated."

echo "Deleting existing frontend Kubernetes resources (if any)..."
kubectl delete --ignore-not-found=true -f k8s-wasp/frontend
echo "Existing frontend resources deleted (or none found)."

echo "Deploying frontend Kubernetes manifests..."
kubectl apply -f k8s-wasp/frontend
echo "Frontend deployment complete."

echo "Deployment finished successfully for version $VERSION!" 